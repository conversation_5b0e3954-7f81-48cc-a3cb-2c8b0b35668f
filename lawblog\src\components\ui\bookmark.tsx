'use client';

import { useState, useEffect } from 'react';
import { Bookmark, BookmarkCheck } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

interface BookmarkProps {
  articleId: string;
  articleTitle?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

export function BookmarkButton({ 
  articleId, 
  articleTitle,
  className,
  size = 'md',
  showLabel = false 
}: BookmarkProps) {
  const t = useTranslations();
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Check if article is bookmarked on component mount
    const bookmarks = getBookmarks();
    setIsBookmarked(bookmarks.some(bookmark => bookmark.id === articleId));
  }, [articleId]);

  const getBookmarks = () => {
    if (typeof window === 'undefined') return [];
    const stored = localStorage.getItem('afersata_bookmarks');
    return stored ? JSON.parse(stored) : [];
  };

  const saveBookmarks = (bookmarks: any[]) => {
    if (typeof window === 'undefined') return;
    localStorage.setItem('afersata_bookmarks', JSON.stringify(bookmarks));
  };

  const toggleBookmark = async () => {
    setIsLoading(true);
    
    try {
      const bookmarks = getBookmarks();
      
      if (isBookmarked) {
        // Remove bookmark
        const updatedBookmarks = bookmarks.filter(bookmark => bookmark.id !== articleId);
        saveBookmarks(updatedBookmarks);
        setIsBookmarked(false);
        toast.success(t('Articles.bookmarkRemoved'));
      } else {
        // Add bookmark
        const newBookmark = {
          id: articleId,
          title: articleTitle || 'Untitled Article',
          bookmarkedAt: new Date().toISOString()
        };
        const updatedBookmarks = [...bookmarks, newBookmark];
        saveBookmarks(updatedBookmarks);
        setIsBookmarked(true);
        toast.success(t('Articles.bookmarkAdded'));
      }
    } catch (error) {
      toast.error(t('common.error'));
    } finally {
      setIsLoading(false);
    }
  };

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const buttonSizeClasses = {
    sm: 'p-1',
    md: 'p-2',
    lg: 'p-3'
  };

  return (
    <button
      onClick={toggleBookmark}
      disabled={isLoading}
      className={cn(
        "relative group transition-all duration-300 rounded-lg",
        "hover:bg-gray-100 dark:hover:bg-gray-800",
        "focus:outline-none focus:ring-2 focus:ring-blue-500",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        buttonSizeClasses[size],
        className
      )}
      title={isBookmarked ? t('Articles.removeBookmark') : t('Articles.addBookmark')}
    >
      <div className="flex items-center space-x-2">
        <div className="relative">
          {isBookmarked ? (
            <BookmarkCheck 
              className={cn(
                sizeClasses[size],
                "text-blue-600 dark:text-blue-400 transition-all duration-300",
                "group-hover:scale-110"
              )} 
            />
          ) : (
            <Bookmark 
              className={cn(
                sizeClasses[size],
                "text-gray-400 dark:text-gray-500 transition-all duration-300",
                "group-hover:text-blue-600 dark:group-hover:text-blue-400",
                "group-hover:scale-110"
              )} 
            />
          )}
          
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
            </div>
          )}
        </div>
        
        {showLabel && (
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {isBookmarked ? t('Articles.bookmarked') : t('Articles.bookmark')}
          </span>
        )}
      </div>
    </button>
  );
}

// Hook for managing bookmarks
export function useBookmarks() {
  const [bookmarks, setBookmarks] = useState<any[]>([]);

  useEffect(() => {
    const stored = getBookmarks();
    setBookmarks(stored);
  }, []);

  const getBookmarks = () => {
    if (typeof window === 'undefined') return [];
    const stored = localStorage.getItem('afersata_bookmarks');
    return stored ? JSON.parse(stored) : [];
  };

  const addBookmark = (article: { id: string; title: string; [key: string]: any }) => {
    const bookmarks = getBookmarks();
    const newBookmark = {
      ...article,
      bookmarkedAt: new Date().toISOString()
    };
    const updatedBookmarks = [...bookmarks, newBookmark];
    localStorage.setItem('afersata_bookmarks', JSON.stringify(updatedBookmarks));
    setBookmarks(updatedBookmarks);
  };

  const removeBookmark = (articleId: string) => {
    const bookmarks = getBookmarks();
    const updatedBookmarks = bookmarks.filter(bookmark => bookmark.id !== articleId);
    localStorage.setItem('afersata_bookmarks', JSON.stringify(updatedBookmarks));
    setBookmarks(updatedBookmarks);
  };

  const isBookmarked = (articleId: string) => {
    return bookmarks.some(bookmark => bookmark.id === articleId);
  };

  const clearBookmarks = () => {
    localStorage.removeItem('afersata_bookmarks');
    setBookmarks([]);
  };

  return {
    bookmarks,
    addBookmark,
    removeBookmark,
    isBookmarked,
    clearBookmarks
  };
}
