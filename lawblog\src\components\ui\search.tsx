'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Search, Filter, X, Tag, User, Calendar, University } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchFilters {
  query: string;
  category: string;
  author: string;
  university: string;
  dateRange: string;
  sortBy: string;
}

interface SearchProps {
  onSearch: (filters: SearchFilters) => void;
  placeholder?: string;
  className?: string;
  showFilters?: boolean;
}

export function SearchComponent({ onSearch, placeholder, className, showFilters = true }: SearchProps) {
  const t = useTranslations();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    category: '',
    author: '',
    university: '',
    dateRange: '',
    sortBy: 'newest'
  });

  const handleSearch = (newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onSearch(updatedFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      query: '',
      category: '',
      author: '',
      university: '',
      dateRange: '',
      sortBy: 'newest'
    };
    setFilters(clearedFilters);
    onSearch(clearedFilters);
  };

  const hasActiveFilters = filters.category || filters.author || filters.university || 
    filters.dateRange || filters.sortBy !== 'newest';

  return (
    <div className={cn("relative w-full", className)}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          value={filters.query}
          onChange={(e) => handleSearch({ query: e.target.value })}
          placeholder={placeholder || "Search articles..."}
          className="w-full pl-10 pr-12 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500"
        />
        
        {showFilters && (
          <button
            type="button"
            onClick={() => setIsModalOpen(true)}
            className={cn(
              "absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-lg transition-all duration-300",
              hasActiveFilters 
                ? "bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400" 
                : "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            )}
            title="Advanced Search"
          >
            <Filter className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-3 flex flex-wrap gap-2">
          {filters.category && (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
              <Tag className="w-3 h-3 mr-1" />
              {filters.category}
              <button
                onClick={() => handleSearch({ category: '' })}
                className="ml-1 hover:text-blue-600 dark:hover:text-blue-300"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          )}
          {filters.author && (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
              <User className="w-3 h-3 mr-1" />
              {filters.author}
              <button
                onClick={() => handleSearch({ author: '' })}
                className="ml-1 hover:text-green-600 dark:hover:text-green-300"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          )}
          <button
            onClick={clearFilters}
            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
          >
            Clear All
          </button>
        </div>
      )}

      {/* Advanced Search Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
          <div 
            className="w-full max-w-4xl bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Advanced Search
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Find exactly what you're looking for with detailed filters
                </p>
              </div>
              <button
                onClick={() => setIsModalOpen(false)}
                className="p-2 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              <div className="space-y-6">
                {/* Search Query */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    <Search className="w-4 h-4 inline mr-2" />
                    Search Query
                  </label>
                  <input
                    type="text"
                    value={filters.query}
                    onChange={(e) => handleSearch({ query: e.target.value })}
                    placeholder="Enter keywords, topics, or phrases..."
                    className="w-full p-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-gray-900 dark:text-white"
                  />
                </div>

                {/* Filters Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Category Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      <Tag className="w-4 h-4 inline mr-2" />
                      Legal Category
                    </label>
                    <select
                      value={filters.category}
                      onChange={(e) => handleSearch({ category: e.target.value })}
                      className="w-full p-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-gray-900 dark:text-white"
                    >
                      <option value="">All Categories</option>
                      <option value="constitutional">Constitutional Law</option>
                      <option value="criminal">Criminal Law</option>
                      <option value="civil">Civil Law</option>
                      <option value="commercial">Commercial Law</option>
                      <option value="international">International Law</option>
                      <option value="environmental">Environmental Law</option>
                      <option value="corporate">Corporate Law</option>
                      <option value="human-rights">Human Rights</option>
                    </select>
                  </div>

                  {/* Author Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      <User className="w-4 h-4 inline mr-2" />
                      Author
                    </label>
                    <input
                      type="text"
                      value={filters.author}
                      onChange={(e) => handleSearch({ author: e.target.value })}
                      placeholder="Author name..."
                      className="w-full p-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-gray-900 dark:text-white"
                    />
                  </div>

                  {/* University Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      <University className="w-4 h-4 inline mr-2" />
                      University
                    </label>
                    <select
                      value={filters.university}
                      onChange={(e) => handleSearch({ university: e.target.value })}
                      className="w-full p-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-gray-900 dark:text-white"
                    >
                      <option value="">All Universities</option>
                      <option value="harvard">Harvard Law School</option>
                      <option value="yale">Yale Law School</option>
                      <option value="stanford">Stanford Law School</option>
                      <option value="columbia">Columbia Law School</option>
                      <option value="nyu">NYU Law School</option>
                      <option value="georgetown">Georgetown Law</option>
                    </select>
                  </div>

                  {/* Date Range Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      <Calendar className="w-4 h-4 inline mr-2" />
                      Date Range
                    </label>
                    <select
                      value={filters.dateRange}
                      onChange={(e) => handleSearch({ dateRange: e.target.value })}
                      className="w-full p-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-gray-900 dark:text-white"
                    >
                      <option value="">All Time</option>
                      <option value="today">Today</option>
                      <option value="week">This Week</option>
                      <option value="month">This Month</option>
                      <option value="year">This Year</option>
                    </select>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={clearFilters}
                    className="px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium"
                  >
                    Clear All Filters
                  </button>
                  <div className="flex space-x-3">
                    <button
                      onClick={() => setIsModalOpen(false)}
                      className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 font-medium"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => {
                        onSearch(filters);
                        setIsModalOpen(false);
                      }}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                    >
                      Apply Filters
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
