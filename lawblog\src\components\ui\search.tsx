'use client';

import { useState, useEffect, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { Search, Filter, X, Tag, User, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchFilters {
  query: string;
  category: string;
  author: string;
  dateRange: string;
  sortBy: string;
}

interface SearchProps {
  onSearch: (filters: SearchFilters) => void;
  placeholder?: string;
  className?: string;
  showFilters?: boolean;
}

export function SearchComponent({ onSearch, placeholder, className, showFilters = true }: SearchProps) {
  const t = useTranslations();
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    category: '',
    author: '',
    dateRange: '',
    sortBy: 'newest'
  });
  
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearch = (newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onSearch(updatedFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      query: '',
      category: '',
      author: '',
      dateRange: '',
      sortBy: 'newest'
    };
    setFilters(clearedFilters);
    onSearch(clearedFilters);
  };

  const hasActiveFilters = filters.category || filters.author || filters.dateRange || filters.sortBy !== 'newest';

  return (
    <div ref={searchRef} className={cn("relative w-full", className)}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          value={filters.query}
          onChange={(e) => handleSearch({ query: e.target.value })}
          placeholder={placeholder || t('common.search')}
          className="w-full pl-10 pr-12 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500"
          onFocus={() => showFilters && setIsOpen(true)}
        />
        
        {showFilters && (
          <button
            type="button"
            onClick={() => setIsOpen(!isOpen)}
            className={cn(
              "absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-lg transition-all duration-300",
              isOpen ? "bg-blue-500 text-white" : "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",
              hasActiveFilters && !isOpen && "bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400"
            )}
            title="Toggle filters"
          >
            <Filter className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Filters Panel */}
      {showFilters && isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-900 rounded-xl p-6 shadow-xl border-2 border-gray-300 dark:border-gray-600 z-[9999] opacity-100">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('Articles.filters')}
            </h3>
            <button
              type="button"
              onClick={() => setIsOpen(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              title="Close filters"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Tag className="w-4 h-4 inline mr-1" />
                {t('Articles.category')}
              </label>
              <select
                value={filters.category}
                onChange={(e) => handleSearch({ category: e.target.value })}
                className="w-full p-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-500 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-gray-900 dark:text-white opacity-100"
              >
                <option value="">{t('Articles.allCategories')}</option>
                <option value="constitutional">Constitutional Law</option>
                <option value="criminal">Criminal Law</option>
                <option value="civil">Civil Law</option>
                <option value="commercial">Commercial Law</option>
                <option value="international">International Law</option>
              </select>
            </div>

            {/* Author Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <User className="w-4 h-4 inline mr-1" />
                {t('Articles.author')}
              </label>
              <input
                type="text"
                value={filters.author}
                onChange={(e) => handleSearch({ author: e.target.value })}
                placeholder={t('Articles.authorPlaceholder')}
                className="w-full p-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-500 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-gray-900 dark:text-white placeholder-gray-500 opacity-100"
              />
            </div>

            {/* Date Range Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Calendar className="w-4 h-4 inline mr-1" />
                {t('Articles.dateRange')}
              </label>
              <select
                value={filters.dateRange}
                onChange={(e) => handleSearch({ dateRange: e.target.value })}
                className="w-full p-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-500 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-gray-900 dark:text-white opacity-100"
              >
                <option value="">{t('Articles.allTime')}</option>
                <option value="today">{t('Articles.today')}</option>
                <option value="week">{t('Articles.thisWeek')}</option>
                <option value="month">{t('Articles.thisMonth')}</option>
                <option value="year">{t('Articles.thisYear')}</option>
              </select>
            </div>

            {/* Sort By */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('Articles.sortBy')}
              </label>
              <select
                value={filters.sortBy}
                onChange={(e) => handleSearch({ sortBy: e.target.value })}
                className="w-full p-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-500 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-gray-900 dark:text-white opacity-100"
              >
                <option value="newest">{t('Articles.newest')}</option>
                <option value="oldest">{t('Articles.oldest')}</option>
                <option value="popular">{t('Articles.mostPopular')}</option>
                <option value="alphabetical">{t('Articles.alphabetical')}</option>
              </select>
            </div>
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={clearFilters}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium px-3 py-1 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
              >
                {t('Articles.clearFilters')}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
