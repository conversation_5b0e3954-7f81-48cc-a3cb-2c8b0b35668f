'use client';

import { useState } from 'react';
import { Share2, Twitter, Facebook, Linkedin, Link, Copy, Check } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

interface SocialShareProps {
  url: string;
  title: string;
  description?: string;
  className?: string;
  variant?: 'button' | 'inline' | 'floating';
  size?: 'sm' | 'md' | 'lg';
}

export function SocialShare({ 
  url, 
  title, 
  description = '',
  className,
  variant = 'button',
  size = 'md'
}: SocialShareProps) {
  const t = useTranslations();
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  const shareData = {
    url: encodeURIComponent(url),
    title: encodeURIComponent(title),
    description: encodeURIComponent(description)
  };

  const shareLinks = {
    twitter: `https://twitter.com/intent/tweet?text=${shareData.title}&url=${shareData.url}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${shareData.url}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${shareData.url}`,
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      toast.success(t('Articles.linkCopied'));
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error(t('common.error'));
    }
  };

  const handleShare = async (platform?: string) => {
    if (platform && shareLinks[platform as keyof typeof shareLinks]) {
      window.open(shareLinks[platform as keyof typeof shareLinks], '_blank', 'width=600,height=400');
    } else if (navigator.share) {
      try {
        await navigator.share({
          title,
          text: description,
          url
        });
      } catch (error) {
        // User cancelled or error occurred
      }
    } else {
      setIsOpen(!isOpen);
    }
  };

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const buttonSizeClasses = {
    sm: 'p-2 text-sm',
    md: 'p-3 text-base',
    lg: 'p-4 text-lg'
  };

  if (variant === 'inline') {
    return (
      <div className={cn("flex items-center space-x-3", className)}>
        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
          {t('Articles.share')}:
        </span>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleShare('twitter')}
            className="p-2 rounded-lg bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-200"
            title="Share on Twitter"
          >
            <Twitter className={sizeClasses[size]} />
          </button>
          <button
            onClick={() => handleShare('facebook')}
            className="p-2 rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition-colors duration-200"
            title="Share on Facebook"
          >
            <Facebook className={sizeClasses[size]} />
          </button>
          <button
            onClick={() => handleShare('linkedin')}
            className="p-2 rounded-lg bg-blue-700 hover:bg-blue-800 text-white transition-colors duration-200"
            title="Share on LinkedIn"
          >
            <Linkedin className={sizeClasses[size]} />
          </button>
          <button
            onClick={copyToClipboard}
            className="p-2 rounded-lg bg-gray-500 hover:bg-gray-600 text-white transition-colors duration-200"
            title="Copy link"
          >
            {copied ? <Check className={sizeClasses[size]} /> : <Link className={sizeClasses[size]} />}
          </button>
        </div>
      </div>
    );
  }

  if (variant === 'floating') {
    return (
      <div className={cn("fixed right-4 top-1/2 transform -translate-y-1/2 z-40", className)}>
        <div className="flex flex-col space-y-2">
          <button
            onClick={() => handleShare('twitter')}
            className="p-3 rounded-full bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
            title="Share on Twitter"
          >
            <Twitter className="w-5 h-5" />
          </button>
          <button
            onClick={() => handleShare('facebook')}
            className="p-3 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
            title="Share on Facebook"
          >
            <Facebook className="w-5 h-5" />
          </button>
          <button
            onClick={() => handleShare('linkedin')}
            className="p-3 rounded-full bg-blue-700 hover:bg-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
            title="Share on LinkedIn"
          >
            <Linkedin className="w-5 h-5" />
          </button>
          <button
            onClick={copyToClipboard}
            className="p-3 rounded-full bg-gray-500 hover:bg-gray-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
            title="Copy link"
          >
            {copied ? <Check className="w-5 h-5" /> : <Link className="w-5 h-5" />}
          </button>
        </div>
      </div>
    );
  }

  // Default button variant
  return (
    <div className={cn("relative", className)}>
      <button
        onClick={() => handleShare()}
        className={cn(
          "flex items-center space-x-2 glass rounded-lg transition-all duration-300",
          "hover:bg-gray-100 dark:hover:bg-gray-800",
          "focus:outline-none focus:ring-2 focus:ring-blue-500",
          buttonSizeClasses[size]
        )}
      >
        <Share2 className={sizeClasses[size]} />
        <span className="font-medium">{t('Articles.share')}</span>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 glass rounded-xl p-4 shadow-xl z-50 min-w-[200px]">
          <div className="space-y-2">
            <button
              onClick={() => handleShare('twitter')}
              className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-200"
            >
              <Twitter className="w-5 h-5 text-blue-500" />
              <span>Twitter</span>
            </button>
            <button
              onClick={() => handleShare('facebook')}
              className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-200"
            >
              <Facebook className="w-5 h-5 text-blue-600" />
              <span>Facebook</span>
            </button>
            <button
              onClick={() => handleShare('linkedin')}
              className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-200"
            >
              <Linkedin className="w-5 h-5 text-blue-700" />
              <span>LinkedIn</span>
            </button>
            <button
              onClick={copyToClipboard}
              className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200"
            >
              {copied ? <Check className="w-5 h-5 text-green-500" /> : <Copy className="w-5 h-5 text-gray-500" />}
              <span>{copied ? t('Articles.copied') : t('Articles.copyLink')}</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
