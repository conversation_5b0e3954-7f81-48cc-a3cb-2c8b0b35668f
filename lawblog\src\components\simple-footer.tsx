'use client'

import Link from 'next/link'
import { BookOpen, Mail, Globe, Heart } from 'lucide-react'

export function SimpleFooter() {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    explore: [
      { name: 'Articles', href: '/articles' },
      { name: 'Writers', href: '/writers' },
      { name: 'Categories', href: '/categories' },
    ],
    legal: [
      { name: 'Constitutional Law', href: '/categories/constitutional-law' },
      { name: 'Criminal Law', href: '/categories/criminal-law' },
      { name: 'Corporate Law', href: '/categories/corporate-law' },
      { name: 'Environmental Law', href: '/categories/environmental-law' },
    ],
    about: [
      { name: 'About አፈርሳታ', href: '/about' },
      { name: 'Contact', href: '/contact' },
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
    ]
  }

  return (
    <footer className="bg-gray-900 dark:bg-gray-950 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold font-ethiopic">አፈርሳታ</span>
            </div>
            <p className="text-gray-400 dark:text-gray-500 mb-6 leading-relaxed">
              Where legal minds share stories. Discover insightful articles written by talented law students from around the world.
            </p>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1 text-sm text-gray-400 dark:text-gray-500">
                <Globe className="w-4 h-4" />
                <span>Available in English & አማርኛ</span>
              </div>
            </div>
          </div>

          {/* Explore */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Explore</h3>
            <ul className="space-y-3">
              {footerLinks.explore.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 dark:text-gray-500 hover:text-white dark:hover:text-gray-300 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Topics */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Legal Topics</h3>
            <ul className="space-y-3">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 dark:text-gray-500 hover:text-white dark:hover:text-gray-300 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* About */}
          <div>
            <h3 className="text-lg font-semibold mb-4">About</h3>
            <ul className="space-y-3">
              {footerLinks.about.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 dark:text-gray-500 hover:text-white dark:hover:text-gray-300 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
            
            {/* Contact */}
            <div className="mt-6 pt-6 border-t border-gray-800 dark:border-gray-700">
              <div className="flex items-center space-x-2 text-gray-400 dark:text-gray-500">
                <Mail className="w-4 h-4" />
                <span className="text-sm"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-gray-800 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-1 text-gray-400 dark:text-gray-500 text-sm">
              <span>© {currentYear} አፈርሳታ. Made with</span>
              <Heart className="w-4 h-4 text-red-500" />
              <span>for legal education.</span>
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-gray-400 dark:text-gray-500">
              <span>Powered by DeepSeek AI</span>
              <Link
                href="/admin/login"
                className="hover:text-white dark:hover:text-gray-300 transition-colors"
              >
                Admin Portal
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
