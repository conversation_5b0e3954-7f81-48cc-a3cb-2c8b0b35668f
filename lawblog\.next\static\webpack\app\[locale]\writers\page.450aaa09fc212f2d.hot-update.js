"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/writers/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/writers/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/[locale]/writers/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WritersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _components_blog_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/blog-navigation */ \"(app-pages-browser)/./src/components/blog-navigation.tsx\");\n/* harmony import */ var _components_blog_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/blog-footer */ \"(app-pages-browser)/./src/components/blog-footer.tsx\");\n/* harmony import */ var _components_ui_reading_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/reading-progress */ \"(app-pages-browser)/./src/components/ui/reading-progress.tsx\");\n/* harmony import */ var _components_ui_search__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/search */ \"(app-pages-browser)/./src/components/ui/search.tsx\");\n/* harmony import */ var _components_ui_writer_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/writer-card */ \"(app-pages-browser)/./src/components/ui/writer-card.tsx\");\n/* harmony import */ var _components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/page-transition */ \"(app-pages-browser)/./src/components/ui/page-transition.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_career_highlights__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/career-highlights */ \"(app-pages-browser)/./src/components/ui/career-highlights.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Mock data for demonstration - replace with actual data fetching\nconst mockWriters = [\n    {\n        id: '1',\n        name: 'Sarah Johnson',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop',\n        bio: 'Passionate about constitutional law and digital rights. Currently researching the intersection of technology and privacy law for my law review note.',\n        university: 'Harvard Law School',\n        specialization: 'Constitutional Law & Technology',\n        location: 'Cambridge, MA',\n        articlesCount: 12,\n        totalViews: 15420,\n        joinedDate: '2023-09-15',\n        achievements: [\n            'Dean\\'s List',\n            'Law Review Editor',\n            'Moot Court Champion'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/sarahjohnson',\n            twitter: 'https://twitter.com/sarahjlaw'\n        },\n        featured: true,\n        // Career-focused additions\n        yearOfStudy: '3L (Third Year)',\n        gpa: '3.85',\n        lawReview: true,\n        mootCourt: true,\n        internships: [\n            'ACLU Summer Intern',\n            'DOJ Civil Rights Division'\n        ],\n        publications: [\n            'Harvard Law Review Note on Digital Privacy',\n            'Constitutional Law Quarterly Article'\n        ],\n        awards: [\n            'Dean\\'s List (6 semesters)',\n            'Constitutional Law Prize',\n            'Best Oralist - National Moot Court'\n        ],\n        barAdmissions: [\n            'Massachusetts (Expected 2024)'\n        ],\n        languages: [\n            'English (Native)',\n            'Spanish (Fluent)',\n            'French (Conversational)'\n        ],\n        interests: [\n            'Digital Rights',\n            'Privacy Law',\n            'First Amendment',\n            'Technology Policy'\n        ],\n        careerGoals: 'Seeking a federal clerkship followed by a position at a top-tier firm specializing in constitutional litigation and technology law.',\n        resumeUrl: '/resumes/sarah-johnson-resume.pdf',\n        portfolioUrl: '/portfolios/sarah-johnson'\n    },\n    {\n        id: '2',\n        name: 'Michael Chen',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop',\n        bio: 'International trade law enthusiast with a focus on Asia-Pacific economic relations and cross-border commerce regulations. Seeking to bridge legal practice with international business.',\n        university: 'Stanford Law School',\n        specialization: 'International Trade Law',\n        location: 'Palo Alto, CA',\n        articlesCount: 8,\n        totalViews: 12350,\n        joinedDate: '2023-08-20',\n        achievements: [\n            'International Law Society President',\n            'Trade Law Certificate'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/michaelchen'\n        },\n        featured: false,\n        yearOfStudy: '2L (Second Year)',\n        gpa: '3.72',\n        lawReview: false,\n        mootCourt: true,\n        internships: [\n            'WTO Legal Affairs Intern',\n            'Baker McKenzie Summer Associate'\n        ],\n        publications: [\n            'Stanford Journal of International Law Comment'\n        ],\n        awards: [\n            'International Law Society Outstanding Service',\n            'Asia-Pacific Law Student Award'\n        ],\n        barAdmissions: [\n            'California (Expected 2025)'\n        ],\n        languages: [\n            'English (Native)',\n            'Mandarin (Native)',\n            'Japanese (Intermediate)'\n        ],\n        interests: [\n            'International Trade',\n            'WTO Law',\n            'Cross-border M&A',\n            'Asia-Pacific Relations'\n        ],\n        careerGoals: 'Aiming for a position at an international law firm with strong Asia-Pacific practice, focusing on trade and investment law.',\n        resumeUrl: '/resumes/michael-chen-resume.pdf'\n    },\n    {\n        id: '3',\n        name: 'Emily Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=200&fit=crop',\n        bio: 'Criminal justice reform advocate working on policy research and community outreach programs for underserved populations. Committed to using law as a tool for social justice.',\n        university: 'Yale Law School',\n        specialization: 'Criminal Law & Policy',\n        location: 'New Haven, CT',\n        articlesCount: 15,\n        totalViews: 18750,\n        joinedDate: '2023-07-10',\n        achievements: [\n            'Public Interest Fellowship',\n            'Criminal Justice Clinic',\n            'Pro Bono Award'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/emilyrodriguez',\n            website: 'https://emilyrodriguez.law'\n        },\n        featured: true,\n        yearOfStudy: '3L (Third Year)',\n        gpa: '3.91',\n        lawReview: true,\n        mootCourt: false,\n        internships: [\n            'Public Defender\\'s Office',\n            'ACLU Criminal Law Reform Project',\n            'Innocence Project'\n        ],\n        publications: [\n            'Yale Law Journal Note on Sentencing Reform',\n            'Criminal Justice Policy Brief'\n        ],\n        awards: [\n            'Public Interest Fellowship',\n            'Outstanding Pro Bono Service (500+ hours)',\n            'Criminal Justice Reform Prize'\n        ],\n        barAdmissions: [\n            'Connecticut (Expected 2024)',\n            'New York (Expected 2024)'\n        ],\n        languages: [\n            'English (Native)',\n            'Spanish (Native)',\n            'Portuguese (Conversational)'\n        ],\n        interests: [\n            'Criminal Justice Reform',\n            'Sentencing Policy',\n            'Wrongful Convictions',\n            'Community Justice'\n        ],\n        careerGoals: 'Dedicated to public interest law, seeking a position with a public defender\\'s office or criminal justice reform organization.',\n        resumeUrl: '/resumes/emily-rodriguez-resume.pdf',\n        portfolioUrl: '/portfolios/emily-rodriguez'\n    },\n    {\n        id: '4',\n        name: 'David Kim',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop',\n        bio: 'Environmental law researcher focusing on climate change litigation and sustainable development policies.',\n        university: 'Columbia Law School',\n        specialization: 'Environmental Law',\n        location: 'New York, NY',\n        articlesCount: 10,\n        totalViews: 14200,\n        joinedDate: '2023-06-25',\n        achievements: [\n            'Environmental Law Review',\n            'Climate Justice Award'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/davidkim'\n        },\n        featured: false\n    },\n    {\n        id: '5',\n        name: 'Lisa Wang',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop',\n        bio: 'Corporate governance specialist with expertise in securities regulation and business ethics compliance.',\n        university: 'NYU Law School',\n        specialization: 'Corporate Law',\n        location: 'New York, NY',\n        articlesCount: 9,\n        totalViews: 11800,\n        joinedDate: '2023-05-15',\n        achievements: [\n            'Business Law Society VP',\n            'Securities Law Certificate'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/lisawang'\n        },\n        featured: false\n    },\n    {\n        id: '6',\n        name: 'Ahmed Hassan',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop',\n        bio: 'Human rights advocate specializing in international humanitarian law and refugee protection policies.',\n        university: 'Georgetown Law',\n        specialization: 'Human Rights Law',\n        location: 'Washington, DC',\n        articlesCount: 13,\n        totalViews: 16900,\n        joinedDate: '2023-04-30',\n        achievements: [\n            'Human Rights Fellowship',\n            'International Law Clinic',\n            'Refugee Advocacy Award'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/ahmedhassan',\n            twitter: 'https://twitter.com/ahmedlaw'\n        },\n        featured: true\n    }\n];\nconst stats = [\n    {\n        label: 'Law Students',\n        value: '150+',\n        icon: _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        label: 'Top Law Schools',\n        value: '50+',\n        icon: _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        label: 'Published Articles',\n        value: '500+',\n        icon: _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        label: 'Career Opportunities',\n        value: '1M+',\n        icon: _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    }\n];\nfunction WritersPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_14__.useTranslations)();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [filteredWriters, setFilteredWriters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockWriters);\n    const handleSearch = (filters)=>{\n        console.log('Search filters:', filters);\n    // Implement filtering logic here\n    // For now, just log the filters\n    };\n    const featuredWriters = filteredWriters.filter((writer)=>writer.featured);\n    const regularWriters = filteredWriters.filter((writer)=>!writer.featured);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_reading_progress__WEBPACK_IMPORTED_MODULE_4__.ReadingProgress, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_navigation__WEBPACK_IMPORTED_MODULE_2__.BlogNavigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"gradient-text\",\n                                                    children: \"Future Legal Leaders\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto\",\n                                                children: \"Discover talented law students from top universities showcasing their expertise through published articles. Connect with future lawyers, explore their academic achievements, and view their professional portfolios.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 text-sm text-gray-500 dark:text-gray-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Resume-ready profiles • Portfolio showcases • Career networking\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    delay: 200,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12\",\n                                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-card p-6 text-center hover-lift\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        className: \"w-8 h-8 mx-auto mb-3 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white mb-1\",\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    delay: 400,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-2xl mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search__WEBPACK_IMPORTED_MODULE_5__.SearchComponent, {\n                                            onSearch: handleSearch,\n                                            placeholder: \"Search writers by name, university, or specialization...\",\n                                            showFilters: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    featuredWriters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50 dark:bg-gray-800/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                                                children: \"Rising Legal Stars\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-gray-600 dark:text-gray-400\",\n                                                children: \"Outstanding law students with exceptional academic records and professional achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 text-sm text-gray-500 dark:text-gray-400\",\n                                                children: \"These students have demonstrated excellence in academics, publications, and professional experience\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.StaggeredList, {\n                                    delay: 150,\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: featuredWriters.map((writer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_writer_card__WEBPACK_IMPORTED_MODULE_6__.WriterCard, {\n                                            writer: writer,\n                                            variant: \"detailed\",\n                                            locale: \"en\"\n                                        }, writer.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_career_highlights__WEBPACK_IMPORTED_MODULE_9__.CareerHighlights, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"All Writers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            filteredWriters.length,\n                                                            \" writers found\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setViewMode('grid'),\n                                                        className: \"p-2 rounded-lg transition-colors duration-200 \".concat(viewMode === 'grid' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'),\n                                                        title: \"Grid View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setViewMode('list'),\n                                                        className: \"p-2 rounded-lg transition-colors duration-200 \".concat(viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'),\n                                                        title: \"List View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-4',\n                                        children: [\n                                            ...Array(6)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.WriterCardSkeleton, {}, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, void 0))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.StaggeredList, {\n                                        delay: 100,\n                                        className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-4',\n                                        children: regularWriters.map((writer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_writer_card__WEBPACK_IMPORTED_MODULE_6__.WriterCard, {\n                                                writer: writer,\n                                                variant: viewMode === 'list' ? 'compact' : 'default',\n                                                locale: \"en\"\n                                            }, writer.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_footer__WEBPACK_IMPORTED_MODULE_3__.BlogFooter, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(WritersPage, \"eW31QzDEIbyNN9UsN0Tr/aSg45c=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_14__.useTranslations\n    ];\n});\n_c = WritersPage;\nvar _c;\n$RefreshReg$(_c, \"WritersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/writers/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/career-highlights.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/career-highlights.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AcademicCredentials: () => (/* binding */ AcademicCredentials),\n/* harmony export */   CareerHighlights: () => (/* binding */ CareerHighlights),\n/* harmony export */   CareerMetrics: () => (/* binding */ CareerMetrics),\n/* harmony export */   ResumeActions: () => (/* binding */ ResumeActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Download,FileText,GraduationCap,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Download,FileText,GraduationCap,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Download,FileText,GraduationCap,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Download,FileText,GraduationCap,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Download,FileText,GraduationCap,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Download,FileText,GraduationCap,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _page_transition__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./page-transition */ \"(app-pages-browser)/./src/components/ui/page-transition.tsx\");\n/* __next_internal_client_entry_do_not_use__ CareerHighlights,CareerMetrics,ResumeActions,AcademicCredentials auto */ \n\n\nfunction CareerHighlights(param) {\n    let { className = '' } = param;\n    const highlights = [\n        {\n            icon: _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: 'Academic Excellence',\n            description: 'Top law students from prestigious universities',\n            stats: '3.7+ Average GPA'\n        },\n        {\n            icon: _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: 'Professional Recognition',\n            description: 'Law review editors, moot court champions, and award winners',\n            stats: '85% on Law Review'\n        },\n        {\n            icon: _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: 'Published Authors',\n            description: 'Students with published articles and legal commentary',\n            stats: '500+ Publications'\n        },\n        {\n            icon: _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: 'Career Ready',\n            description: 'Internship experience at top firms and organizations',\n            stats: '90% Placement Rate'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_page_transition__WEBPACK_IMPORTED_MODULE_1__.FadeIn, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                                children: \"Why Recruit From Our Platform?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                                children: \"Connect with the next generation of legal talent. Our writers are carefully vetted law students with proven academic excellence and professional potential.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: highlights.map((highlight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_page_transition__WEBPACK_IMPORTED_MODULE_1__.FadeIn, {\n                            delay: index * 150,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass-card p-6 text-center hover-lift\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(highlight.icon, {\n                                            className: \"w-8 h-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-gray-900 dark:text-white mb-2\",\n                                        children: highlight.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mb-3 text-sm\",\n                                        children: highlight.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: highlight.stats\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_page_transition__WEBPACK_IMPORTED_MODULE_1__.FadeIn, {\n                    delay: 600,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"glass-card p-8 max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                                    children: \"For Legal Recruiters & Employers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                    children: \"Access detailed profiles, academic records, writing samples, and professional portfolios. Find the perfect candidates for your internship programs, entry-level positions, and clerkships.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-primary px-8 py-3 rounded-xl font-semibold text-white hover-lift flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Browse All Profiles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"glass border border-gray-200 dark:border-gray-700 px-8 py-3 rounded-xl font-semibold text-gray-900 dark:text-white hover-lift flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Download Talent Guide\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_c = CareerHighlights;\nfunction CareerMetrics(param) {\n    let { writer, className = '' } = param;\n    var _writer_publications, _writer_internships, _writer_awards;\n    const metrics = [\n        {\n            label: 'GPA',\n            value: writer.gpa || 'N/A',\n            icon: _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: 'text-blue-600 dark:text-blue-400'\n        },\n        {\n            label: 'Publications',\n            value: ((_writer_publications = writer.publications) === null || _writer_publications === void 0 ? void 0 : _writer_publications.length) || 0,\n            icon: _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'text-green-600 dark:text-green-400'\n        },\n        {\n            label: 'Internships',\n            value: ((_writer_internships = writer.internships) === null || _writer_internships === void 0 ? void 0 : _writer_internships.length) || 0,\n            icon: _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'text-purple-600 dark:text-purple-400'\n        },\n        {\n            label: 'Awards',\n            value: ((_writer_awards = writer.awards) === null || _writer_awards === void 0 ? void 0 : _writer_awards.length) || 0,\n            icon: _barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: 'text-yellow-600 dark:text-yellow-400'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 \".concat(className),\n        children: metrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(metric.icon, {\n                        className: \"w-6 h-6 mx-auto mb-2 \".concat(metric.color)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                        children: metric.value\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: metric.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CareerMetrics;\nfunction ResumeActions(param) {\n    let { resumeUrl, portfolioUrl, className = '' } = param;\n    if (!resumeUrl && !portfolioUrl) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-3 \".concat(className),\n        children: [\n            resumeUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: resumeUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-semibold hover-lift transition-all duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Download Resume\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this),\n            portfolioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: portfolioUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl font-semibold hover-lift transition-all duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"View Portfolio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ResumeActions;\nfunction AcademicCredentials(param) {\n    let { writer, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                        children: writer.university\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-600 dark:text-blue-400 font-medium\",\n                        children: writer.specialization\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    writer.yearOfStudy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: writer.yearOfStudy\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center space-x-4 text-sm\",\n                children: [\n                    writer.gpa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-4 h-4 text-blue-600 dark:text-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: [\n                                    \"GPA: \",\n                                    writer.gpa\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    writer.lawReview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: \"Law Review\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this),\n                    writer.mootCourt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Download_FileText_GraduationCap_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 text-green-600 dark:text-green-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: \"Moot Court\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\ui\\\\career-highlights.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_c3 = AcademicCredentials;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"CareerHighlights\");\n$RefreshReg$(_c1, \"CareerMetrics\");\n$RefreshReg$(_c2, \"ResumeActions\");\n$RefreshReg$(_c3, \"AcademicCredentials\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2NhcmVlci1oaWdobGlnaHRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRXFHO0FBQzFEO0FBTXBDLFNBQVNPLGlCQUFpQixLQUF5QztRQUF6QyxFQUFFQyxZQUFZLEVBQUUsRUFBeUIsR0FBekM7SUFDL0IsTUFBTUMsYUFBYTtRQUNqQjtZQUNFQyxNQUFNVixnSUFBYUE7WUFDbkJXLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxPQUFPO1FBQ1Q7UUFDQTtZQUNFSCxNQUFNVCxnSUFBS0E7WUFDWFUsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLE9BQU87UUFDVDtRQUNBO1lBQ0VILE1BQU1SLGdJQUFRQTtZQUNkUyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsT0FBTztRQUNUO1FBQ0E7WUFDRUgsTUFBTVAsZ0lBQUtBO1lBQ1hRLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxPQUFPO1FBQ1Q7S0FDRDtJQUVELHFCQUNFLDhEQUFDQztRQUFRTixXQUFXLHFIQUErSCxPQUFWQTtrQkFDdkksNEVBQUNPO1lBQUlQLFdBQVU7OzhCQUNiLDhEQUFDRixvREFBTUE7OEJBQ0wsNEVBQUNTO3dCQUFJUCxXQUFVOzswQ0FDYiw4REFBQ1E7Z0NBQUdSLFdBQVU7MENBQXdEOzs7Ozs7MENBR3RFLDhEQUFDUztnQ0FBRVQsV0FBVTswQ0FBNkQ7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU85RSw4REFBQ087b0JBQUlQLFdBQVU7OEJBQ1pDLFdBQVdTLEdBQUcsQ0FBQyxDQUFDQyxXQUFXQyxzQkFDMUIsOERBQUNkLG9EQUFNQTs0QkFBYWUsT0FBT0QsUUFBUTtzQ0FDakMsNEVBQUNMO2dDQUFJUCxXQUFVOztrREFDYiw4REFBQ087d0NBQUlQLFdBQVU7a0RBQ2IsNEVBQUNXLFVBQVVULElBQUk7NENBQUNGLFdBQVU7Ozs7Ozs7Ozs7O2tEQUU1Qiw4REFBQ2M7d0NBQUdkLFdBQVU7a0RBQ1hXLFVBQVVSLEtBQUs7Ozs7OztrREFFbEIsOERBQUNNO3dDQUFFVCxXQUFVO2tEQUNWVyxVQUFVUCxXQUFXOzs7Ozs7a0RBRXhCLDhEQUFDRzt3Q0FBSVAsV0FBVTtrREFDWlcsVUFBVU4sS0FBSzs7Ozs7Ozs7Ozs7OzJCQVpUTzs7Ozs7Ozs7Ozs4QkFvQmpCLDhEQUFDZCxvREFBTUE7b0JBQUNlLE9BQU87OEJBQ2IsNEVBQUNOO3dCQUFJUCxXQUFVO2tDQUNiLDRFQUFDTzs0QkFBSVAsV0FBVTs7OENBQ2IsOERBQUNjO29DQUFHZCxXQUFVOzhDQUF3RDs7Ozs7OzhDQUd0RSw4REFBQ1M7b0NBQUVULFdBQVU7OENBQXdDOzs7Ozs7OENBSXJELDhEQUFDTztvQ0FBSVAsV0FBVTs7c0RBQ2IsOERBQUNlOzRDQUFPZixXQUFVOzs4REFDaEIsOERBQUNMLGdJQUFLQTtvREFBQ0ssV0FBVTs7Ozs7OzhEQUNqQiw4REFBQ2dCOzhEQUFLOzs7Ozs7Ozs7Ozs7c0RBRVIsOERBQUNEOzRDQUFPZixXQUFVOzs4REFDaEIsOERBQUNILGdJQUFRQTtvREFBQ0csV0FBVTs7Ozs7OzhEQUNwQiw4REFBQ2dCOzhEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTeEI7S0EzRmdCakI7QUEwR1QsU0FBU2tCLGNBQWMsS0FBOEM7UUFBOUMsRUFBRUMsTUFBTSxFQUFFbEIsWUFBWSxFQUFFLEVBQXNCLEdBQTlDO1FBVWpCa0Isc0JBTUFBLHFCQU1BQTtJQXJCWCxNQUFNQyxVQUFVO1FBQ2Q7WUFDRUMsT0FBTztZQUNQQyxPQUFPSCxPQUFPSSxHQUFHLElBQUk7WUFDckJwQixNQUFNVixnSUFBYUE7WUFDbkIrQixPQUFPO1FBQ1Q7UUFDQTtZQUNFSCxPQUFPO1lBQ1BDLE9BQU9ILEVBQUFBLHVCQUFBQSxPQUFPTSxZQUFZLGNBQW5CTiwyQ0FBQUEscUJBQXFCTyxNQUFNLEtBQUk7WUFDdEN2QixNQUFNUixnSUFBUUE7WUFDZDZCLE9BQU87UUFDVDtRQUNBO1lBQ0VILE9BQU87WUFDUEMsT0FBT0gsRUFBQUEsc0JBQUFBLE9BQU9RLFdBQVcsY0FBbEJSLDBDQUFBQSxvQkFBb0JPLE1BQU0sS0FBSTtZQUNyQ3ZCLE1BQU1QLGdJQUFLQTtZQUNYNEIsT0FBTztRQUNUO1FBQ0E7WUFDRUgsT0FBTztZQUNQQyxPQUFPSCxFQUFBQSxpQkFBQUEsT0FBT1MsTUFBTSxjQUFiVCxxQ0FBQUEsZUFBZU8sTUFBTSxLQUFJO1lBQ2hDdkIsTUFBTVQsZ0lBQUtBO1lBQ1g4QixPQUFPO1FBQ1Q7S0FDRDtJQUVELHFCQUNFLDhEQUFDaEI7UUFBSVAsV0FBVyx5Q0FBbUQsT0FBVkE7a0JBQ3REbUIsUUFBUVQsR0FBRyxDQUFDLENBQUNrQixRQUFRaEIsc0JBQ3BCLDhEQUFDTDtnQkFBZ0JQLFdBQVU7O2tDQUN6Qiw4REFBQzRCLE9BQU8xQixJQUFJO3dCQUFDRixXQUFXLHdCQUFxQyxPQUFiNEIsT0FBT0wsS0FBSzs7Ozs7O2tDQUM1RCw4REFBQ2hCO3dCQUFJUCxXQUFVO2tDQUNaNEIsT0FBT1AsS0FBSzs7Ozs7O2tDQUVmLDhEQUFDZDt3QkFBSVAsV0FBVTtrQ0FDWjRCLE9BQU9SLEtBQUs7Ozs7Ozs7ZUFOUFI7Ozs7Ozs7Ozs7QUFZbEI7TUEzQ2dCSztBQW9EVCxTQUFTWSxjQUFjLEtBQStEO1FBQS9ELEVBQUVDLFNBQVMsRUFBRUMsWUFBWSxFQUFFL0IsWUFBWSxFQUFFLEVBQXNCLEdBQS9EO0lBQzVCLElBQUksQ0FBQzhCLGFBQWEsQ0FBQ0MsY0FBYztRQUMvQixPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ3hCO1FBQUlQLFdBQVcsbUNBQTZDLE9BQVZBOztZQUNoRDhCLDJCQUNDLDhEQUFDRTtnQkFDQ0MsTUFBTUg7Z0JBQ05JLFFBQU87Z0JBQ1BDLEtBQUk7Z0JBQ0puQyxXQUFVOztrQ0FFViw4REFBQ0gsZ0lBQVFBO3dCQUFDRyxXQUFVOzs7Ozs7a0NBQ3BCLDhEQUFDZ0I7a0NBQUs7Ozs7Ozs7Ozs7OztZQUdUZSw4QkFDQyw4REFBQ0M7Z0JBQ0NDLE1BQU1GO2dCQUNORyxRQUFPO2dCQUNQQyxLQUFJO2dCQUNKbkMsV0FBVTs7a0NBRVYsOERBQUNKLGdJQUFRQTt3QkFBQ0ksV0FBVTs7Ozs7O2tDQUNwQiw4REFBQ2dCO2tDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLaEI7TUEvQmdCYTtBQThDVCxTQUFTTyxvQkFBb0IsS0FBb0Q7UUFBcEQsRUFBRWxCLE1BQU0sRUFBRWxCLFlBQVksRUFBRSxFQUE0QixHQUFwRDtJQUNsQyxxQkFDRSw4REFBQ087UUFBSVAsV0FBVyxhQUF1QixPQUFWQTs7MEJBQzNCLDhEQUFDTztnQkFBSVAsV0FBVTs7a0NBQ2IsOERBQUNjO3dCQUFHZCxXQUFVO2tDQUNYa0IsT0FBT21CLFVBQVU7Ozs7OztrQ0FFcEIsOERBQUM1Qjt3QkFBRVQsV0FBVTtrQ0FDVmtCLE9BQU9vQixjQUFjOzs7Ozs7b0JBRXZCcEIsT0FBT3FCLFdBQVcsa0JBQ2pCLDhEQUFDOUI7d0JBQUVULFdBQVU7a0NBQ1ZrQixPQUFPcUIsV0FBVzs7Ozs7Ozs7Ozs7OzBCQUt6Qiw4REFBQ2hDO2dCQUFJUCxXQUFVOztvQkFDWmtCLE9BQU9JLEdBQUcsa0JBQ1QsOERBQUNmO3dCQUFJUCxXQUFVOzswQ0FDYiw4REFBQ1IsZ0lBQWFBO2dDQUFDUSxXQUFVOzs7Ozs7MENBQ3pCLDhEQUFDZ0I7Z0NBQUtoQixXQUFVOztvQ0FBbUM7b0NBQU1rQixPQUFPSSxHQUFHOzs7Ozs7Ozs7Ozs7O29CQUd0RUosT0FBT3NCLFNBQVMsa0JBQ2YsOERBQUNqQzt3QkFBSVAsV0FBVTs7MENBQ2IsOERBQUNOLGdJQUFRQTtnQ0FBQ00sV0FBVTs7Ozs7OzBDQUNwQiw4REFBQ2dCO2dDQUFLaEIsV0FBVTswQ0FBbUM7Ozs7Ozs7Ozs7OztvQkFHdERrQixPQUFPdUIsU0FBUyxrQkFDZiw4REFBQ2xDO3dCQUFJUCxXQUFVOzswQ0FDYiw4REFBQ1AsZ0lBQUtBO2dDQUFDTyxXQUFVOzs7Ozs7MENBQ2pCLDhEQUFDZ0I7Z0NBQUtoQixXQUFVOzBDQUFtQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTS9EO01BdkNnQm9DIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEtyYWltYXRpY1xcRGVza3RvcFxcYWZlcnNhdGFcXGxhd2Jsb2dcXHNyY1xcY29tcG9uZW50c1xcdWlcXGNhcmVlci1oaWdobGlnaHRzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IEdyYWR1YXRpb25DYXAsIEF3YXJkLCBCb29rT3BlbiwgVXNlcnMsIFRyZW5kaW5nVXAsIEZpbGVUZXh0LCBEb3dubG9hZCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBGYWRlSW4gfSBmcm9tICcuL3BhZ2UtdHJhbnNpdGlvbic7XG5cbmludGVyZmFjZSBDYXJlZXJIaWdobGlnaHRzUHJvcHMge1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBDYXJlZXJIaWdobGlnaHRzKHsgY2xhc3NOYW1lID0gJycgfTogQ2FyZWVySGlnaGxpZ2h0c1Byb3BzKSB7XG4gIGNvbnN0IGhpZ2hsaWdodHMgPSBbXG4gICAge1xuICAgICAgaWNvbjogR3JhZHVhdGlvbkNhcCxcbiAgICAgIHRpdGxlOiAnQWNhZGVtaWMgRXhjZWxsZW5jZScsXG4gICAgICBkZXNjcmlwdGlvbjogJ1RvcCBsYXcgc3R1ZGVudHMgZnJvbSBwcmVzdGlnaW91cyB1bml2ZXJzaXRpZXMnLFxuICAgICAgc3RhdHM6ICczLjcrIEF2ZXJhZ2UgR1BBJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogQXdhcmQsXG4gICAgICB0aXRsZTogJ1Byb2Zlc3Npb25hbCBSZWNvZ25pdGlvbicsXG4gICAgICBkZXNjcmlwdGlvbjogJ0xhdyByZXZpZXcgZWRpdG9ycywgbW9vdCBjb3VydCBjaGFtcGlvbnMsIGFuZCBhd2FyZCB3aW5uZXJzJyxcbiAgICAgIHN0YXRzOiAnODUlIG9uIExhdyBSZXZpZXcnXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiBCb29rT3BlbixcbiAgICAgIHRpdGxlOiAnUHVibGlzaGVkIEF1dGhvcnMnLFxuICAgICAgZGVzY3JpcHRpb246ICdTdHVkZW50cyB3aXRoIHB1Ymxpc2hlZCBhcnRpY2xlcyBhbmQgbGVnYWwgY29tbWVudGFyeScsXG4gICAgICBzdGF0czogJzUwMCsgUHVibGljYXRpb25zJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogVXNlcnMsXG4gICAgICB0aXRsZTogJ0NhcmVlciBSZWFkeScsXG4gICAgICBkZXNjcmlwdGlvbjogJ0ludGVybnNoaXAgZXhwZXJpZW5jZSBhdCB0b3AgZmlybXMgYW5kIG9yZ2FuaXphdGlvbnMnLFxuICAgICAgc3RhdHM6ICc5MCUgUGxhY2VtZW50IFJhdGUnXG4gICAgfVxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPHNlY3Rpb24gY2xhc3NOYW1lPXtgcHktMTYgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHZpYS13aGl0ZSB0by1wdXJwbGUtNTAgZGFyazpmcm9tLWdyYXktOTAwIGRhcms6dmlhLWdyYXktOTAwIGRhcms6dG8tZ3JheS04MDAgJHtjbGFzc05hbWV9YH0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgIDxGYWRlSW4+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgIFdoeSBSZWNydWl0IEZyb20gT3VyIFBsYXRmb3JtP1xuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWF4LXctM3hsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgQ29ubmVjdCB3aXRoIHRoZSBuZXh0IGdlbmVyYXRpb24gb2YgbGVnYWwgdGFsZW50LiBPdXIgd3JpdGVycyBhcmUgY2FyZWZ1bGx5IHZldHRlZCBsYXcgc3R1ZGVudHMgXG4gICAgICAgICAgICAgIHdpdGggcHJvdmVuIGFjYWRlbWljIGV4Y2VsbGVuY2UgYW5kIHByb2Zlc3Npb25hbCBwb3RlbnRpYWwuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvRmFkZUluPlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtOFwiPlxuICAgICAgICAgIHtoaWdobGlnaHRzLm1hcCgoaGlnaGxpZ2h0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPEZhZGVJbiBrZXk9e2luZGV4fSBkZWxheT17aW5kZXggKiAxNTB9PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzLWNhcmQgcC02IHRleHQtY2VudGVyIGhvdmVyLWxpZnRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBteC1hdXRvIG1iLTQgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwMCB0by1wdXJwbGUtNjAwIHJvdW5kZWQtMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8aGlnaGxpZ2h0Lmljb24gY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgbWItMlwiPlxuICAgICAgICAgICAgICAgICAge2hpZ2hsaWdodC50aXRsZX1cbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTMgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAge2hpZ2hsaWdodC5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtoaWdobGlnaHQuc3RhdHN9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9GYWRlSW4+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDYWxsIHRvIEFjdGlvbiBmb3IgUmVjcnVpdGVycyAqL31cbiAgICAgICAgPEZhZGVJbiBkZWxheT17NjAwfT5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTE2IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzLWNhcmQgcC04IG1heC13LTR4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgbWItNFwiPlxuICAgICAgICAgICAgICAgIEZvciBMZWdhbCBSZWNydWl0ZXJzICYgRW1wbG95ZXJzXG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICBBY2Nlc3MgZGV0YWlsZWQgcHJvZmlsZXMsIGFjYWRlbWljIHJlY29yZHMsIHdyaXRpbmcgc2FtcGxlcywgYW5kIHByb2Zlc3Npb25hbCBwb3J0Zm9saW9zLiBcbiAgICAgICAgICAgICAgICBGaW5kIHRoZSBwZXJmZWN0IGNhbmRpZGF0ZXMgZm9yIHlvdXIgaW50ZXJuc2hpcCBwcm9ncmFtcywgZW50cnktbGV2ZWwgcG9zaXRpb25zLCBhbmQgY2xlcmtzaGlwcy5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTQganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5IHB4LTggcHktMyByb3VuZGVkLXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBob3Zlci1saWZ0IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+QnJvd3NlIEFsbCBQcm9maWxlczwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImdsYXNzIGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgcHgtOCBweS0zIHJvdW5kZWQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBob3Zlci1saWZ0IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+RG93bmxvYWQgVGFsZW50IEd1aWRlPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0ZhZGVJbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKTtcbn1cblxuLy8gQ29tcG9uZW50IGZvciBpbmRpdmlkdWFsIGNhcmVlciBtZXRyaWNzXG5pbnRlcmZhY2UgQ2FyZWVyTWV0cmljc1Byb3BzIHtcbiAgd3JpdGVyOiB7XG4gICAgZ3BhPzogc3RyaW5nO1xuICAgIGxhd1Jldmlldz86IGJvb2xlYW47XG4gICAgbW9vdENvdXJ0PzogYm9vbGVhbjtcbiAgICBpbnRlcm5zaGlwcz86IHN0cmluZ1tdO1xuICAgIHB1YmxpY2F0aW9ucz86IHN0cmluZ1tdO1xuICAgIGF3YXJkcz86IHN0cmluZ1tdO1xuICB9O1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBDYXJlZXJNZXRyaWNzKHsgd3JpdGVyLCBjbGFzc05hbWUgPSAnJyB9OiBDYXJlZXJNZXRyaWNzUHJvcHMpIHtcbiAgY29uc3QgbWV0cmljcyA9IFtcbiAgICB7XG4gICAgICBsYWJlbDogJ0dQQScsXG4gICAgICB2YWx1ZTogd3JpdGVyLmdwYSB8fCAnTi9BJyxcbiAgICAgIGljb246IEdyYWR1YXRpb25DYXAsXG4gICAgICBjb2xvcjogJ3RleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgbGFiZWw6ICdQdWJsaWNhdGlvbnMnLFxuICAgICAgdmFsdWU6IHdyaXRlci5wdWJsaWNhdGlvbnM/Lmxlbmd0aCB8fCAwLFxuICAgICAgaWNvbjogQm9va09wZW4sXG4gICAgICBjb2xvcjogJ3RleHQtZ3JlZW4tNjAwIGRhcms6dGV4dC1ncmVlbi00MDAnXG4gICAgfSxcbiAgICB7XG4gICAgICBsYWJlbDogJ0ludGVybnNoaXBzJyxcbiAgICAgIHZhbHVlOiB3cml0ZXIuaW50ZXJuc2hpcHM/Lmxlbmd0aCB8fCAwLFxuICAgICAgaWNvbjogVXNlcnMsXG4gICAgICBjb2xvcjogJ3RleHQtcHVycGxlLTYwMCBkYXJrOnRleHQtcHVycGxlLTQwMCdcbiAgICB9LFxuICAgIHtcbiAgICAgIGxhYmVsOiAnQXdhcmRzJyxcbiAgICAgIHZhbHVlOiB3cml0ZXIuYXdhcmRzPy5sZW5ndGggfHwgMCxcbiAgICAgIGljb246IEF3YXJkLFxuICAgICAgY29sb3I6ICd0ZXh0LXllbGxvdy02MDAgZGFyazp0ZXh0LXllbGxvdy00MDAnXG4gICAgfVxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BncmlkIGdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00ICR7Y2xhc3NOYW1lfWB9PlxuICAgICAge21ldHJpY3MubWFwKChtZXRyaWMsIGluZGV4KSA9PiAoXG4gICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC00IGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTgwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgPG1ldHJpYy5pY29uIGNsYXNzTmFtZT17YHctNiBoLTYgbXgtYXV0byBtYi0yICR7bWV0cmljLmNvbG9yfWB9IC8+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgIHttZXRyaWMudmFsdWV9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICB7bWV0cmljLmxhYmVsfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICkpfVxuICAgIDwvZGl2PlxuICApO1xufVxuXG4vLyBDb21wb25lbnQgZm9yIHJlc3VtZS9wb3J0Zm9saW8gYWN0aW9uc1xuaW50ZXJmYWNlIFJlc3VtZUFjdGlvbnNQcm9wcyB7XG4gIHJlc3VtZVVybD86IHN0cmluZztcbiAgcG9ydGZvbGlvVXJsPzogc3RyaW5nO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBSZXN1bWVBY3Rpb25zKHsgcmVzdW1lVXJsLCBwb3J0Zm9saW9VcmwsIGNsYXNzTmFtZSA9ICcnIH06IFJlc3VtZUFjdGlvbnNQcm9wcykge1xuICBpZiAoIXJlc3VtZVVybCAmJiAhcG9ydGZvbGlvVXJsKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtMyAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHtyZXN1bWVVcmwgJiYgKFxuICAgICAgICA8YVxuICAgICAgICAgIGhyZWY9e3Jlc3VtZVVybH1cbiAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgcHktMyBweC00IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1ibHVlLTcwMCB0ZXh0LXdoaXRlIHJvdW5kZWQteGwgZm9udC1zZW1pYm9sZCBob3Zlci1saWZ0IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgID5cbiAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgPHNwYW4+RG93bmxvYWQgUmVzdW1lPC9zcGFuPlxuICAgICAgICA8L2E+XG4gICAgICApfVxuICAgICAge3BvcnRmb2xpb1VybCAmJiAoXG4gICAgICAgIDxhXG4gICAgICAgICAgaHJlZj17cG9ydGZvbGlvVXJsfVxuICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXG4gICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtMiBweS0zIHB4LTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXB1cnBsZS02MDAgdG8tcHVycGxlLTcwMCB0ZXh0LXdoaXRlIHJvdW5kZWQteGwgZm9udC1zZW1pYm9sZCBob3Zlci1saWZ0IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgID5cbiAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgPHNwYW4+VmlldyBQb3J0Zm9saW88L3NwYW4+XG4gICAgICAgIDwvYT5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG5cbi8vIENvbXBvbmVudCBmb3IgYWNhZGVtaWMgY3JlZGVudGlhbHMgZGlzcGxheVxuaW50ZXJmYWNlIEFjYWRlbWljQ3JlZGVudGlhbHNQcm9wcyB7XG4gIHdyaXRlcjoge1xuICAgIHVuaXZlcnNpdHk6IHN0cmluZztcbiAgICB5ZWFyT2ZTdHVkeT86IHN0cmluZztcbiAgICBncGE/OiBzdHJpbmc7XG4gICAgbGF3UmV2aWV3PzogYm9vbGVhbjtcbiAgICBtb290Q291cnQ/OiBib29sZWFuO1xuICAgIHNwZWNpYWxpemF0aW9uOiBzdHJpbmc7XG4gIH07XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEFjYWRlbWljQ3JlZGVudGlhbHMoeyB3cml0ZXIsIGNsYXNzTmFtZSA9ICcnIH06IEFjYWRlbWljQ3JlZGVudGlhbHNQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgc3BhY2UteS0zICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAge3dyaXRlci51bml2ZXJzaXR5fVxuICAgICAgICA8L2gzPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGRhcms6dGV4dC1ibHVlLTQwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgIHt3cml0ZXIuc3BlY2lhbGl6YXRpb259XG4gICAgICAgIDwvcD5cbiAgICAgICAge3dyaXRlci55ZWFyT2ZTdHVkeSAmJiAoXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAge3dyaXRlci55ZWFyT2ZTdHVkeX1cbiAgICAgICAgICA8L3A+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIHNwYWNlLXgtNCB0ZXh0LXNtXCI+XG4gICAgICAgIHt3cml0ZXIuZ3BhICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgPEdyYWR1YXRpb25DYXAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNjAwIGRhcms6dGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPkdQQToge3dyaXRlci5ncGF9PC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgICB7d3JpdGVyLmxhd1JldmlldyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgIDxCb29rT3BlbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtcHVycGxlLTYwMCBkYXJrOnRleHQtcHVycGxlLTQwMFwiIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPkxhdyBSZXZpZXc8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICAgIHt3cml0ZXIubW9vdENvdXJ0ICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgPEF3YXJkIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmVlbi02MDAgZGFyazp0ZXh0LWdyZWVuLTQwMFwiIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPk1vb3QgQ291cnQ8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJHcmFkdWF0aW9uQ2FwIiwiQXdhcmQiLCJCb29rT3BlbiIsIlVzZXJzIiwiRmlsZVRleHQiLCJEb3dubG9hZCIsIkZhZGVJbiIsIkNhcmVlckhpZ2hsaWdodHMiLCJjbGFzc05hbWUiLCJoaWdobGlnaHRzIiwiaWNvbiIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJzdGF0cyIsInNlY3Rpb24iLCJkaXYiLCJoMiIsInAiLCJtYXAiLCJoaWdobGlnaHQiLCJpbmRleCIsImRlbGF5IiwiaDMiLCJidXR0b24iLCJzcGFuIiwiQ2FyZWVyTWV0cmljcyIsIndyaXRlciIsIm1ldHJpY3MiLCJsYWJlbCIsInZhbHVlIiwiZ3BhIiwiY29sb3IiLCJwdWJsaWNhdGlvbnMiLCJsZW5ndGgiLCJpbnRlcm5zaGlwcyIsImF3YXJkcyIsIm1ldHJpYyIsIlJlc3VtZUFjdGlvbnMiLCJyZXN1bWVVcmwiLCJwb3J0Zm9saW9VcmwiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCIsIkFjYWRlbWljQ3JlZGVudGlhbHMiLCJ1bml2ZXJzaXR5Iiwic3BlY2lhbGl6YXRpb24iLCJ5ZWFyT2ZTdHVkeSIsImxhd1JldmlldyIsIm1vb3RDb3VydCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/career-highlights.tsx\n"));

/***/ })

});