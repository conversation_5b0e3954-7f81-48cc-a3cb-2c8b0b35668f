'use client';

import { useTranslations } from 'next-intl';
import { ArticleCard } from './article-card';
import { FadeIn, StaggeredList } from './page-transition';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';

interface Article {
  id: string;
  title: string;
  excerpt: string;
  image?: string;
  author: {
    name: string;
    avatar?: string;
    university?: string;
  };
  category: string;
  publishedAt: string;
  readingTime: number;
  views?: number;
  tags?: string[];
}

interface RelatedArticlesProps {
  articles: Article[];
  currentArticleId?: string;
  title?: string;
  maxArticles?: number;
  locale?: string;
  className?: string;
}

export function RelatedArticles({ 
  articles, 
  currentArticleId,
  title,
  maxArticles = 3,
  locale = 'en',
  className = ''
}: RelatedArticlesProps) {
  const t = useTranslations();

  // Filter out current article and limit results
  const relatedArticles = articles
    .filter(article => article.id !== currentArticleId)
    .slice(0, maxArticles);

  if (relatedArticles.length === 0) {
    return null;
  }

  return (
    <section className={`py-16 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeIn>
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {title || t('Articles.relatedArticles')}
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400">
                {t('Articles.relatedArticlesSubtitle')}
              </p>
            </div>
            
            <Link 
              href={`/${locale}/articles`}
              className="hidden sm:flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
            >
              <span>{t('Articles.viewAll')}</span>
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </FadeIn>

        <StaggeredList 
          delay={150}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {relatedArticles.map((article) => (
            <ArticleCard 
              key={article.id}
              article={article}
              variant="default"
              showBookmark={true}
              showShare={false}
              locale={locale}
            />
          ))}
        </StaggeredList>

        {/* Mobile view all link */}
        <FadeIn delay={600}>
          <div className="text-center mt-8 sm:hidden">
            <Link 
              href={`/${locale}/articles`}
              className="inline-flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
            >
              <span>{t('Articles.viewAll')}</span>
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </FadeIn>
      </div>
    </section>
  );
}

// Hook for fetching related articles
export function useRelatedArticles(currentArticle: Article, allArticles: Article[]) {
  // Simple algorithm to find related articles based on:
  // 1. Same category
  // 2. Similar tags
  // 3. Same author
  // 4. Recent articles

  const relatedByCategory = allArticles.filter(
    article => 
      article.id !== currentArticle.id && 
      article.category === currentArticle.category
  );

  const relatedByTags = allArticles.filter(
    article => {
      if (article.id === currentArticle.id || !article.tags || !currentArticle.tags) {
        return false;
      }
      
      const commonTags = article.tags.filter(tag => 
        currentArticle.tags!.includes(tag)
      );
      
      return commonTags.length > 0;
    }
  );

  const relatedByAuthor = allArticles.filter(
    article => 
      article.id !== currentArticle.id && 
      article.author.name === currentArticle.author.name
  );

  // Combine and deduplicate
  const relatedArticles = [
    ...relatedByCategory,
    ...relatedByTags.filter(article => 
      !relatedByCategory.some(cat => cat.id === article.id)
    ),
    ...relatedByAuthor.filter(article => 
      !relatedByCategory.some(cat => cat.id === article.id) &&
      !relatedByTags.some(tag => tag.id === article.id)
    )
  ];

  // If we don't have enough related articles, add recent ones
  if (relatedArticles.length < 6) {
    const recentArticles = allArticles
      .filter(article => 
        article.id !== currentArticle.id &&
        !relatedArticles.some(related => related.id === article.id)
      )
      .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
      .slice(0, 6 - relatedArticles.length);
    
    relatedArticles.push(...recentArticles);
  }

  return relatedArticles.slice(0, 6);
}

// Component for article recommendations in sidebar
interface ArticleRecommendationsProps {
  articles: Article[];
  title?: string;
  maxArticles?: number;
  locale?: string;
  className?: string;
}

export function ArticleRecommendations({ 
  articles, 
  title,
  maxArticles = 5,
  locale = 'en',
  className = ''
}: ArticleRecommendationsProps) {
  const t = useTranslations();

  const recommendedArticles = articles.slice(0, maxArticles);

  if (recommendedArticles.length === 0) {
    return null;
  }

  return (
    <div className={`glass-card p-6 ${className}`}>
      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
        {title || t('Articles.recommended')}
      </h3>
      
      <div className="space-y-4">
        {recommendedArticles.map((article, index) => (
          <FadeIn key={article.id} delay={index * 100}>
            <ArticleCard 
              article={article}
              variant="minimal"
              showBookmark={false}
              showShare={false}
              locale={locale}
            />
          </FadeIn>
        ))}
      </div>
      
      <FadeIn delay={500}>
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Link 
            href={`/${locale}/articles`}
            className="flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
          >
            <span>{t('Articles.viewAll')}</span>
            <ArrowRight className="w-4 h-4" />
          </Link>
        </div>
      </FadeIn>
    </div>
  );
}
