import { Suspense } from 'react';
import { useTranslations } from 'next-intl';
import { BlogNavigation } from '@/components/blog-navigation';
import { BlogFooter } from '@/components/blog-footer';
import { ReadingProgress } from '@/components/ui/reading-progress';
import { FadeIn, StaggeredList, ScaleIn } from '@/components/ui/page-transition';
import { ArticleCard } from '@/components/ui/article-card';
import { SearchComponent } from '@/components/ui/search';
import { ArticleCardSkeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import { ArrowRight, BookOpen, Users, TrendingUp, Star } from 'lucide-react';

// Mock data for demonstration
const featuredArticles = [
  {
    id: '1',
    title: 'Constitutional Rights in the Digital Age: Privacy vs Security',
    excerpt: 'An in-depth analysis of how constitutional rights are evolving in response to digital surveillance and data privacy concerns in modern society.',
    image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=800&h=400&fit=crop',
    author: {
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      university: 'Harvard Law School'
    },
    category: 'Constitutional Law',
    publishedAt: '2024-01-15',
    readingTime: 8,
    views: 1250,
    tags: ['privacy', 'digital rights', 'constitution']
  },
  {
    id: '2',
    title: 'International Trade Law: Navigating Global Commerce',
    excerpt: 'Understanding the complexities of international trade agreements and their impact on global economic relationships.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop',
    author: {
      name: 'Michael Chen',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      university: 'Stanford Law School'
    },
    category: 'International Law',
    publishedAt: '2024-01-12',
    readingTime: 12,
    views: 890,
    tags: ['trade', 'international', 'commerce']
  },
  {
    id: '3',
    title: 'Criminal Justice Reform: A Path Forward',
    excerpt: 'Examining current criminal justice policies and proposing evidence-based reforms for a more equitable system.',
    image: 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=800&h=400&fit=crop',
    author: {
      name: 'Emily Rodriguez',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      university: 'Yale Law School'
    },
    category: 'Criminal Law',
    publishedAt: '2024-01-10',
    readingTime: 10,
    views: 1100,
    tags: ['reform', 'justice', 'policy']
  }
];

const stats = [
  { label: 'Articles Published', value: '500+', icon: BookOpen },
  { label: 'Talented Writers', value: '150+', icon: Users },
  { label: 'Monthly Readers', value: '25K+', icon: TrendingUp },
  { label: 'Universities', value: '50+', icon: Star }
];

export default function Home() {
  const t = useTranslations();

  const handleSearch = (filters: any) => {
    console.log('Search filters:', filters);
    // Implement search functionality
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
      <ReadingProgress />
      <BlogNavigation />

      <main className="pt-16">
        {/* Hero Section */}
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
          {/* Animated Background */}
          <div className="absolute inset-0 animated-gradient opacity-10" />
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-white/50 to-purple-50/50 dark:from-gray-900/50 dark:via-gray-900/50 dark:to-gray-800/50" />

          {/* Floating Elements */}
          <div className="absolute top-20 left-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl float" />
          <div className="absolute bottom-20 right-10 w-32 h-32 bg-purple-500/10 rounded-full blur-xl float" style={{ animationDelay: '2s' }} />
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-indigo-500/10 rounded-full blur-xl float" style={{ animationDelay: '4s' }} />

          <div className="relative z-10 text-center px-4 max-w-6xl mx-auto">
            <FadeIn>
              <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold text-gray-900 dark:text-white mb-6 sm:mb-8 leading-tight">
                <span className="gradient-text font-ethiopic block mb-4">
                  አፈርሳታ
                </span>
                <span className="text-2xl sm:text-4xl lg:text-5xl text-gray-700 dark:text-gray-300">
                  {t('HomePage.title')}
                </span>
              </h1>
            </FadeIn>

            <FadeIn delay={200}>
              <p className="text-lg sm:text-xl lg:text-2xl text-gray-600 dark:text-gray-400 mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed">
                {t('HomePage.subtitle')}
              </p>
            </FadeIn>

            <FadeIn delay={400}>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
                <Link
                  href="/articles"
                  className="btn-primary px-8 py-4 rounded-xl text-lg font-semibold text-white hover-lift"
                >
                  {t('HomePage.exploreArticles')}
                  <ArrowRight className="w-5 h-5 ml-2 inline" />
                </Link>
                <Link
                  href="/writers"
                  className="glass px-8 py-4 rounded-xl text-lg font-semibold text-gray-900 dark:text-white hover-lift border border-gray-200 dark:border-gray-700"
                >
                  {t('HomePage.meetWriters')}
                </Link>
              </div>
            </FadeIn>

            {/* Stats */}
            <FadeIn delay={600}>
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto">
                {stats.map((stat, index) => (
                  <ScaleIn key={index} delay={index * 100}>
                    <div className="glass-card p-6 text-center hover-lift">
                      <stat.icon className="w-8 h-8 mx-auto mb-3 text-blue-600 dark:text-blue-400" />
                      <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                        {stat.value}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {stat.label}
                      </div>
                    </div>
                  </ScaleIn>
                ))}
              </div>
            </FadeIn>
          </div>
        </section>

        {/* Search Section */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800/50">
          <div className="max-w-4xl mx-auto px-4">
            <FadeIn>
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Discover Legal Insights
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-400">
                  Search through our extensive collection of legal articles and research
                </p>
              </div>
            </FadeIn>

            <FadeIn delay={200}>
              <SearchComponent
                onSearch={handleSearch}
                placeholder="Search articles, authors, or topics..."
                className="max-w-2xl mx-auto"
              />
            </FadeIn>
          </div>
        </section>

        {/* Featured Articles */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4">
            <FadeIn>
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Featured Articles
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                  Discover the latest insights from our community of talented law students
                </p>
              </div>
            </FadeIn>

            <Suspense fallback={
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {[...Array(3)].map((_, i) => (
                  <ArticleCardSkeleton key={i} />
                ))}
              </div>
            }>
              <StaggeredList delay={150}>
                {featuredArticles.map((article, index) => (
                  <div key={article.id} className={index === 0 ? "lg:col-span-2" : ""}>
                    <ArticleCard
                      article={article}
                      variant={index === 0 ? "featured" : "default"}
                      showBookmark={true}
                      showShare={true}
                      locale="en"
                    />
                  </div>
                ))}
              </StaggeredList>
            </Suspense>

            <FadeIn delay={800}>
              <div className="text-center mt-12">
                <Link
                  href="/articles"
                  className="inline-flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-semibold text-lg transition-colors duration-200"
                >
                  <span>View All Articles</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </div>
            </FadeIn>
          </div>
        </section>
      </main>

      <BlogFooter />
    </div>
  );
}
