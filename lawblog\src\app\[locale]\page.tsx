import { BlogNavigation } from '@/components/blog-navigation'
import { BlogFooter } from '@/components/blog-footer'

export default function Home() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
      <BlogNavigation />
      <main className="pt-16">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 flex items-center justify-center">
          <div className="text-center px-4 max-w-6xl mx-auto">
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold text-gray-900 dark:text-white mb-6 sm:mb-8 leading-tight">
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent font-ethiopic">
                አፈርሳታ
              </span>
              <br />
              <span className="text-2xl sm:text-4xl lg:text-5xl text-gray-700 dark:text-gray-300">
                Where Legal Minds Share Stories
              </span>
            </h1>
            <p className="text-lg sm:text-xl lg:text-2xl text-gray-600 dark:text-gray-400 mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed">
              Discover insightful legal articles written by talented law students from top universities around the world.
            </p>
          </div>
        </div>
      </main>
      <BlogFooter />
    </div>
  )
}
