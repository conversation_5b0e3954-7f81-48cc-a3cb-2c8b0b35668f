'use client';

import { Suspense, useState } from 'react';
import { useTranslations } from 'next-intl';
import { BlogNavigation } from '@/components/blog-navigation';
import { BlogFooter } from '@/components/blog-footer';
import { ReadingProgress } from '@/components/ui/reading-progress';
import { FadeIn, StaggeredList } from '@/components/ui/page-transition';
import { ArticleCard } from '@/components/ui/article-card';
import { SearchComponent } from '@/components/ui/search';
import { ArticleCardSkeleton } from '@/components/ui/skeleton';
import { WriterCard } from '@/components/ui/writer-card';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, BookOpen, Users, TrendingUp, Star, Play, Clock, Eye, Heart, Share2 } from 'lucide-react';



// Mock data for demonstration - replace with actual data fetching
const mockArticles = [
  {
    id: '1',
    title: 'Constitutional Rights in the Digital Age: Privacy vs Security',
    excerpt: 'An in-depth analysis of how constitutional rights are evolving in response to digital surveillance and data privacy concerns in modern society.',
    image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=800&h=400&fit=crop',
    author: {
      name: '<PERSON> <PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      university: 'Harvard Law School'
    },
    category: 'Constitutional Law',
    publishedAt: '2024-01-15',
    readingTime: 8,
    views: 1250,
    tags: ['privacy', 'digital rights', 'constitution']
  },
  {
    id: '2',
    title: 'International Trade Law: Navigating Global Commerce',
    excerpt: 'Understanding the complexities of international trade agreements and their impact on global economic relationships.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop',
    author: {
      name: 'Michael Chen',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      university: 'Stanford Law School'
    },
    category: 'International Law',
    publishedAt: '2024-01-12',
    readingTime: 12,
    views: 890,
    tags: ['trade', 'international', 'commerce']
  },
  {
    id: '3',
    title: 'Criminal Justice Reform: A Path Forward',
    excerpt: 'Examining current criminal justice policies and proposing evidence-based reforms for a more equitable system.',
    image: 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=800&h=400&fit=crop',
    author: {
      name: 'Emily Rodriguez',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      university: 'Yale Law School'
    },
    category: 'Criminal Law',
    publishedAt: '2024-01-10',
    readingTime: 10,
    views: 1100,
    tags: ['reform', 'justice', 'policy']
  },
  {
    id: '4',
    title: 'Environmental Law and Climate Change',
    excerpt: 'Exploring the legal frameworks needed to address climate change and environmental protection in the 21st century.',
    image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?w=800&h=400&fit=crop',
    author: {
      name: 'David Kim',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      university: 'Columbia Law School'
    },
    category: 'Environmental Law',
    publishedAt: '2024-01-08',
    readingTime: 15,
    views: 750,
    tags: ['environment', 'climate', 'sustainability']
  }
];

const stats = [
  { label: 'Articles Published', value: '500+', icon: BookOpen },
  { label: 'Talented Writers', value: '150+', icon: Users },
  { label: 'Monthly Readers', value: '25K+', icon: TrendingUp },
  { label: 'Universities', value: '50+', icon: Star }
];

export default function Home() {
  const t = useTranslations();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [featuredArticle, setFeaturedArticle] = useState(mockArticles[0]);

  const handleSearch = (filters: any) => {
    console.log('Search filters:', filters);
    // Implement search functionality
  };

  const categories = ['all', 'Constitutional Law', 'Criminal Law', 'International Law', 'Environmental Law'];
  const filteredArticles = selectedCategory === 'all'
    ? mockArticles.slice(1) // Exclude featured article
    : mockArticles.filter(article => article.category === selectedCategory);

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
      <ReadingProgress />
      <BlogNavigation />

      <main className="pt-16">
        {/* Hero Blog Section */}
        <section className="relative py-12 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <FadeIn>
              <div className="text-center mb-12">
                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                  <span className="gradient-text font-ethiopic">አፈርሳታ</span>
                  <span className="block text-2xl sm:text-3xl lg:text-4xl mt-2 text-gray-600 dark:text-gray-400">
                    Legal Insights Blog
                  </span>
                </h1>
                <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                  Discover cutting-edge legal analysis from tomorrow's legal leaders
                </p>
              </div>
            </FadeIn>

            {/* Featured Article Hero */}
            <FadeIn delay={200}>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
                {/* Featured Article Content */}
                <div className="order-2 lg:order-1">
                  <div className="flex items-center space-x-2 mb-4">
                    <span className="px-3 py-1 text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-full">
                      FEATURED
                    </span>
                    <span className="px-3 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                      {featuredArticle.category}
                    </span>
                  </div>

                  <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4 leading-tight">
                    {featuredArticle.title}
                  </h2>

                  <p className="text-lg text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                    {featuredArticle.excerpt}
                  </p>

                  {/* Author & Meta */}
                  <div className="flex items-center space-x-4 mb-6">
                    <Image
                      src={featuredArticle.author.avatar}
                      alt={featuredArticle.author.name}
                      width={48}
                      height={48}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {featuredArticle.author.name}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {featuredArticle.author.university}
                      </p>
                    </div>
                  </div>

                  {/* Article Stats */}
                  <div className="flex items-center space-x-6 mb-8 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{featuredArticle.readingTime} min read</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Eye className="w-4 h-4" />
                      <span>{featuredArticle.views?.toLocaleString()} views</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Heart className="w-4 h-4" />
                      <span>124 likes</span>
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Link
                      href={`/en/articles/${featuredArticle.id}`}
                      className="btn-primary px-8 py-4 rounded-xl font-semibold text-white hover-lift flex items-center justify-center space-x-2"
                    >
                      <Play className="w-5 h-5" />
                      <span>Read Full Article</span>
                    </Link>
                    <button type="button" className="glass border border-gray-200 dark:border-gray-700 px-8 py-4 rounded-xl font-semibold text-gray-900 dark:text-white hover-lift flex items-center justify-center space-x-2">
                      <Share2 className="w-5 h-5" />
                      <span>Share Article</span>
                    </button>
                  </div>
                </div>

                {/* Featured Article Image */}
                <div className="order-1 lg:order-2">
                  <div className="relative group">
                    <div className="relative w-full h-80 lg:h-96 rounded-2xl overflow-hidden shadow-2xl">
                      <Image
                        src={featuredArticle.image}
                        alt={featuredArticle.title}
                        fill
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                      <div className="absolute bottom-4 left-4 right-4">
                        <div className="flex items-center justify-between text-white">
                          <span className="text-sm font-medium">
                            Published {new Date(featuredArticle.publishedAt).toLocaleDateString()}
                          </span>
                          <div className="flex items-center space-x-2">
                            {featuredArticle.tags?.slice(0, 2).map((tag, index) => (
                              <span key={index} className="px-2 py-1 text-xs bg-white/20 rounded-full backdrop-blur-sm">
                                #{tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </FadeIn>
          </div>
        </section>

        {/* Category Filter & Latest Articles */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Category Filter */}
            <FadeIn>
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Latest Legal Insights
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                  Browse articles by legal specialization
                </p>

                {/* Category Pills */}
                <div className="flex flex-wrap justify-center gap-3 mb-8">
                  {categories.map((category) => (
                    <button
                      key={category}
                      type="button"
                      onClick={() => setSelectedCategory(category)}
                      className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
                        selectedCategory === category
                          ? 'bg-blue-600 text-white shadow-lg'
                          : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'
                      }`}
                    >
                      {category === 'all' ? 'All Articles' : category}
                    </button>
                  ))}
                </div>
              </div>
            </FadeIn>

            {/* Articles Grid */}
            <Suspense fallback={
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[...Array(6)].map((_, i) => (
                  <ArticleCardSkeleton key={i} />
                ))}
              </div>
            }>
              <StaggeredList
                delay={100}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {filteredArticles.slice(0, 6).map((article) => (
                  <ArticleCard
                    key={article.id}
                    article={article}
                    variant="default"
                    showBookmark={true}
                    showShare={true}
                    locale="en"
                  />
                ))}
              </StaggeredList>
            </Suspense>

            {/* View All Articles */}
            <FadeIn delay={600}>
              <div className="text-center mt-12">
                <Link
                  href="/en/articles"
                  className="btn-primary px-8 py-4 rounded-xl font-semibold text-white hover-lift inline-flex items-center space-x-2"
                >
                  <span>View All Articles</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </div>
            </FadeIn>
          </div>
        </section>

        {/* Search Section */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <FadeIn>
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Find Your Legal Interest
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-400">
                  Search through our extensive collection of legal articles and research
                </p>
              </div>
            </FadeIn>

            <FadeIn delay={200}>
              <SearchComponent
                onSearch={handleSearch}
                placeholder="Search articles, authors, or topics..."
                className="max-w-2xl mx-auto"
                showFilters={true}
              />
            </FadeIn>
          </div>
        </section>

        {/* Featured Writers */}
        <section className="py-16 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <FadeIn>
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Meet Our Writers
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-400">
                  Talented law students from top universities sharing their expertise
                </p>
              </div>
            </FadeIn>

            <StaggeredList
              delay={150}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {mockArticles.slice(0, 3).map((article) => (
                <WriterCard
                  key={article.author.name}
                  writer={{
                    id: article.id,
                    name: article.author.name,
                    avatar: article.author.avatar,
                    bio: `Specializing in ${article.category}. Published ${article.readingTime} articles with ${article.views} total views.`,
                    university: article.author.university,
                    specialization: article.category,
                    articlesCount: 8,
                    totalViews: article.views || 0,
                    joinedDate: article.publishedAt
                  }}
                  variant="compact"
                  locale="en"
                />
              ))}
            </StaggeredList>

            <FadeIn delay={600}>
              <div className="text-center mt-12">
                <Link
                  href="/en/writers"
                  className="glass border border-gray-200 dark:border-gray-700 px-8 py-4 rounded-xl font-semibold text-gray-900 dark:text-white hover-lift inline-flex items-center space-x-2"
                >
                  <Users className="w-5 h-5" />
                  <span>Meet All Writers</span>
                </Link>
              </div>
            </FadeIn>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <FadeIn>
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Platform Impact
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-400">
                  Building the future of legal education and career development
                </p>
              </div>
            </FadeIn>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <FadeIn key={index} delay={index * 100}>
                  <div className="glass-card p-6 text-center hover-lift">
                    <stat.icon className="w-8 h-8 mx-auto mb-3 text-blue-600 dark:text-blue-400" />
                    <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {stat.label}
                    </div>
                  </div>
                </FadeIn>
              ))}
            </div>
          </div>
        </section>
      </main>

      <BlogFooter />
    </div>
  );
}
