import bcrypt from 'bcryptjs'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface AdminUser {
  id: string
  email: string
  name: string
  lastLogin: string | null
  createdAt: string
}

export async function createAdminUser(email: string, password: string, name: string): Promise<AdminUser> {
  const passwordHash = await bcrypt.hash(password, 12)
  
  const { data, error } = await supabase
    .from('admin_users')
    .insert({
      email,
      password_hash: passwordHash,
      name,
    })
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to create admin user: ${error.message}`)
  }

  return {
    id: data.id,
    email: data.email,
    name: data.name,
    lastLogin: data.last_login,
    createdAt: data.created_at,
  }
}

export async function verifyAdminCredentials(email: string, password: string): Promise<AdminUser | null> {
  const { data, error } = await supabase
    .from('admin_users')
    .select('*')
    .eq('email', email)
    .single()

  if (error || !data) {
    return null
  }

  const isValidPassword = await bcrypt.compare(password, data.password_hash)
  if (!isValidPassword) {
    return null
  }

  // Update last login
  await supabase
    .from('admin_users')
    .update({ last_login: new Date().toISOString() })
    .eq('id', data.id)

  return {
    id: data.id,
    email: data.email,
    name: data.name,
    lastLogin: data.last_login,
    createdAt: data.created_at,
  }
}

export async function initializeDefaultAdmin(): Promise<void> {
  const defaultEmail = process.env.ADMIN_EMAIL || '<EMAIL>'
  const defaultPassword = process.env.ADMIN_PASSWORD || 'admin123secure'
  
  // Check if admin already exists
  const { data: existingAdmin } = await supabase
    .from('admin_users')
    .select('id')
    .eq('email', defaultEmail)
    .single()

  if (!existingAdmin) {
    try {
      await createAdminUser(defaultEmail, defaultPassword, 'Admin User')
      console.log('Default admin user created successfully')
    } catch (error) {
      console.error('Failed to create default admin user:', error)
    }
  }
}
