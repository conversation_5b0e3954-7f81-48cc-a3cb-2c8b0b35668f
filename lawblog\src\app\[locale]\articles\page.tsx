'use client';

import { Suspense, useState } from 'react';
import { useTranslations } from 'next-intl';
import { BlogNavigation } from '@/components/blog-navigation';
import { BlogFooter } from '@/components/blog-footer';
import { ReadingProgress } from '@/components/ui/reading-progress';
import { SearchComponent } from '@/components/ui/search';
import { ArticleCard } from '@/components/ui/article-card';
import { FadeIn, StaggeredList } from '@/components/ui/page-transition';
import { ArticleCardSkeleton } from '@/components/ui/skeleton';
import { Filter, Grid, List } from 'lucide-react';

// Mock data for demonstration - replace with actual data fetching
const mockArticles = [
  {
    id: '1',
    title: 'Constitutional Rights in the Digital Age: Privacy vs Security',
    excerpt: 'An in-depth analysis of how constitutional rights are evolving in response to digital surveillance and data privacy concerns in modern society.',
    image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=800&h=400&fit=crop',
    author: {
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=100&h=100&fit=crop&crop=face',
      university: 'Harvard Law School'
    },
    category: 'Constitutional Law',
    publishedAt: '2024-01-15',
    readingTime: 8,
    views: 1250,
    tags: ['privacy', 'digital rights', 'constitution']
  },
  {
    id: '2',
    title: 'International Trade Law: Navigating Global Commerce',
    excerpt: 'Understanding the complexities of international trade agreements and their impact on global economic relationships.',
    image: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=800&h=400&fit=crop',
    author: {
      name: 'Michael Chen',
      avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      university: 'Stanford Law School'
    },
    category: 'International Law',
    publishedAt: '2024-01-12',
    readingTime: 12,
    views: 890,
    tags: ['trade', 'international', 'commerce']
  },
  {
    id: '3',
    title: 'Criminal Justice Reform: A Path Forward',
    excerpt: 'Examining current criminal justice policies and proposing evidence-based reforms for a more equitable system.',
    image: 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=800&h=400&fit=crop',
    author: {
      name: 'Emily Rodriguez',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      university: 'Yale Law School'
    },
    category: 'Criminal Law',
    publishedAt: '2024-01-10',
    readingTime: 10,
    views: 1100,
    tags: ['reform', 'justice', 'policy']
  },
  {
    id: '4',
    title: 'Environmental Law and Climate Change',
    excerpt: 'Exploring the legal frameworks needed to address climate change and environmental protection in the 21st century.',
    image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?w=800&h=400&fit=crop',
    author: {
      name: 'David Kim',
      avatar: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      university: 'Columbia Law School'
    },
    category: 'Environmental Law',
    publishedAt: '2024-01-08',
    readingTime: 15,
    views: 750,
    tags: ['environment', 'climate', 'sustainability']
  },
  {
    id: '5',
    title: 'Corporate Governance in Modern Business',
    excerpt: 'Analyzing the evolving landscape of corporate governance and its impact on business ethics and accountability.',
    image: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=800&h=400&fit=crop',
    author: {
      name: 'Lisa Wang',
      avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=100&h=100&fit=crop&crop=face',
      university: 'NYU Law School'
    },
    category: 'Corporate Law',
    publishedAt: '2024-01-05',
    readingTime: 9,
    views: 920,
    tags: ['corporate', 'governance', 'business']
  },
  {
    id: '6',
    title: 'Human Rights in the Digital Era',
    excerpt: 'Examining how traditional human rights concepts apply to digital spaces and emerging technologies.',
    image: 'https://images.unsplash.com/photo-*************-d95e436ab8d6?w=800&h=400&fit=crop',
    author: {
      name: 'Ahmed Hassan',
      avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      university: 'Georgetown Law'
    },
    category: 'Human Rights',
    publishedAt: '2024-01-03',
    readingTime: 11,
    views: 1050,
    tags: ['human rights', 'digital', 'technology']
  }
];

export default function ArticlesPage() {
  const t = useTranslations();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filteredArticles, setFilteredArticles] = useState(mockArticles);
  const [isSearchDropdownOpen, setIsSearchDropdownOpen] = useState(false);

  const handleSearch = (filters: any) => {
    console.log('Search filters:', filters);
    // Implement filtering logic here
    // For now, just log the filters
  };

  const handleDropdownToggle = (isOpen: boolean) => {
    setIsSearchDropdownOpen(isOpen);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
      <ReadingProgress />
      <div className={`${isSearchDropdownOpen ? 'blur-sm' : ''} transition-all duration-300`}>
        <BlogNavigation />
      </div>

      <main className="pt-16">
        {/* Header Section */}
        <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <FadeIn>
              <div className={`text-center mb-12 ${isSearchDropdownOpen ? 'blur-sm' : ''} transition-all duration-300`}>
                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                  <span className="gradient-text">{t('Articles.title')}</span>
                </h1>
                <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                  {t('Articles.subtitle')}
                </p>
              </div>
            </FadeIn>

            {/* Search and Filters - NO BLUR */}
            <FadeIn delay={200}>
              <div className="max-w-4xl mx-auto">
                <SearchComponent
                  onSearch={handleSearch}
                  placeholder={t('Articles.searchPlaceholder')}
                  showFilters={true}
                  onDropdownToggle={handleDropdownToggle}
                />
              </div>
            </FadeIn>
          </div>
        </section>

        {/* Articles Section */}
        <section className={`py-16 ${isSearchDropdownOpen ? 'blur-sm' : ''} transition-all duration-300`}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* View Toggle */}
            <FadeIn>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 space-y-4 sm:space-y-0">
                <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4">
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                    All Articles
                  </h2>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {filteredArticles.length} articles found
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg transition-colors duration-200 ${
                      viewMode === 'grid'
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                        : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                    }`}
                    title="Grid View"
                  >
                    <Grid className="w-5 h-5" />
                  </button>
                  <button
                    type="button"
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg transition-colors duration-200 ${
                      viewMode === 'list'
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                        : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                    }`}
                    title="List View"
                  >
                    <List className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </FadeIn>

            {/* Articles Grid/List */}
            <Suspense fallback={
              <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6'}>
                {[...Array(6)].map((_, i) => (
                  <ArticleCardSkeleton key={i} />
                ))}
              </div>
            }>
              <StaggeredList
                delay={100}
                className={
                  viewMode === 'grid'
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
                    : 'space-y-6'
                }
              >
                {filteredArticles.map((article) => (
                  <ArticleCard
                    key={article.id}
                    article={article}
                    variant={viewMode === 'list' ? 'compact' : 'default'}
                    showBookmark={true}
                    showShare={true}
                    locale="en"
                  />
                ))}
              </StaggeredList>
            </Suspense>

            {/* Load More */}
            <FadeIn delay={600}>
              <div className="text-center mt-12">
                <button type="button" className="btn-primary px-8 py-3 rounded-xl font-semibold text-white hover-lift">
                  {t('Articles.loadMore')}
                </button>
              </div>
            </FadeIn>
          </div>
        </section>
      </main>

      <div className={`${isSearchDropdownOpen ? 'blur-sm' : ''} transition-all duration-300`}>
        <BlogFooter />
      </div>
    </div>
  );
}
