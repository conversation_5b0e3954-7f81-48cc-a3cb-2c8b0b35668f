import { BlogNavigation } from '@/components/blog-navigation'
import { ArticlesList } from '@/components/articles-list'
import { BlogFooter } from '@/components/blog-footer'
import { getPublishedPosts, getCategories } from '@/lib/blog-service'

export default async function ArticlesPage() {
  const posts = await getPublishedPosts(20)
  const categories = await getCategories()

  return (
    <div className="min-h-screen bg-gray-50">
      <BlogNavigation />
      <main className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              Legal Articles
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
              Explore insightful articles written by talented law students from top universities around the world.
            </p>
          </div>

          <ArticlesList posts={posts} categories={categories} />
        </div>
      </main>
      <BlogFooter />
    </div>
  )
}
