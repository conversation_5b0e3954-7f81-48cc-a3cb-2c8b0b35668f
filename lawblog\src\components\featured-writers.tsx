'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { GraduationCap, BookOpen, Eye, ArrowRight, Users } from 'lucide-react'
import { Writer } from '@/lib/blog-service'

interface FeaturedWritersProps {
  writers: Writer[]
}

export function FeaturedWriters({ writers }: FeaturedWritersProps) {
  const params = useParams()
  const locale = params.locale as string
  const t = useTranslations()

  const createWriterSlug = (name: string) => {
    return name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
  }

  if (writers.length === 0) {
    return null
  }

  return (
    <section className="py-16 sm:py-24 bg-gray-50 dark:bg-gray-800/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12 sm:mb-16">
          <div className="inline-flex items-center space-x-2 bg-purple-100 dark:bg-purple-900/50 px-4 py-2 rounded-full text-sm font-medium text-purple-600 dark:text-purple-400 mb-4">
            <Users className="w-4 h-4" />
            <span>{t('homepage.ourWriters')}</span>
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            {t('writers.title')}
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            {t('homepage.writersSubtitle')}
          </p>
        </div>

        {/* Writers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {writers.map((writer) => (
            <div
              key={writer.id}
              className="bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 group"
            >
              {/* Profile Header */}
              <div className="relative h-32 bg-gradient-to-br from-blue-500 to-purple-600">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                  {writer.avatarUrl ? (
                    <div className="relative w-16 h-16 rounded-full overflow-hidden border-4 border-white shadow-lg">
                      <Image
                        src={writer.avatarUrl}
                        alt={writer.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-16 h-16 rounded-full bg-white border-4 border-white shadow-lg flex items-center justify-center">
                      <span className="text-2xl font-bold text-gray-600">
                        {writer.name.charAt(0)}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Content */}
              <div className="p-6 pt-8 text-center">
                {/* Name and Specialization */}
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-1">
                  {writer.name}
                </h3>
                {writer.specialization && (
                  <p className="text-sm text-blue-600 dark:text-blue-400 mb-3">
                    {writer.specialization}
                  </p>
                )}

                {/* University Info */}
                {writer.university && (
                  <div className="flex items-center justify-center space-x-1 text-sm text-gray-600 dark:text-gray-400 mb-4">
                    <GraduationCap className="w-4 h-4" />
                    <span>{writer.university}</span>
                    {writer.graduationYear && (
                      <span className="text-gray-400 dark:text-gray-500">• {writer.graduationYear}</span>
                    )}
                  </div>
                )}

                {/* Bio */}
                {writer.bio && (
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                    {writer.bio}
                  </p>
                )}

                {/* Stats */}
                <div className="flex items-center justify-center space-x-6 mb-6 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-1 text-blue-600 dark:text-blue-400 mb-1">
                      <BookOpen className="w-4 h-4" />
                      <span className="font-bold text-lg">{writer.totalPosts}</span>
                    </div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{t('writers.totalArticles')}</span>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-1 text-purple-600 dark:text-purple-400 mb-1">
                      <Eye className="w-4 h-4" />
                      <span className="font-bold text-lg">{writer.totalViews.toLocaleString()}</span>
                    </div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{t('writers.totalViews')}</span>
                  </div>
                </div>

                {/* Action Button */}
                <Link
                  href={`/${locale}/writers/${createWriterSlug(writer.name)}`}
                  className="block w-full text-center bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 transform group-hover:scale-105"
                >
                  {t('writers.viewProfile')}
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* View All Writers Button */}
        <div className="text-center mt-12">
          <Link
            href={`/${locale}/writers`}
            className="inline-flex items-center space-x-2 bg-white dark:bg-gray-900 border-2 border-purple-600 dark:border-purple-400 text-purple-600 dark:text-purple-400 hover:bg-purple-600 dark:hover:bg-purple-400 hover:text-white dark:hover:text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105"
          >
            <span>{t('common.viewAll')}</span>
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>

        {/* Call to Action */}
        <div className="mt-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">{t('writers.interestedInWriting')}</h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            {t('writers.writingCTA')}
          </p>
          <div className="text-sm text-blue-100">
            {t('writers.contactAdmin')}
          </div>
        </div>
      </div>
    </section>
  )
}
