"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/articles/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/articles/page.tsx":
/*!********************************************!*\
  !*** ./src/app/[locale]/articles/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArticlesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _components_blog_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/blog-navigation */ \"(app-pages-browser)/./src/components/blog-navigation.tsx\");\n/* harmony import */ var _components_blog_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/blog-footer */ \"(app-pages-browser)/./src/components/blog-footer.tsx\");\n/* harmony import */ var _components_ui_reading_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/reading-progress */ \"(app-pages-browser)/./src/components/ui/reading-progress.tsx\");\n/* harmony import */ var _components_ui_search__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/search */ \"(app-pages-browser)/./src/components/ui/search.tsx\");\n/* harmony import */ var _components_ui_article_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/article-card */ \"(app-pages-browser)/./src/components/ui/article-card.tsx\");\n/* harmony import */ var _components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/page-transition */ \"(app-pages-browser)/./src/components/ui/page-transition.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Grid,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Grid,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Mock data for demonstration - replace with actual data fetching\nconst mockArticles = [\n    {\n        id: '1',\n        title: 'Constitutional Rights in the Digital Age: Privacy vs Security',\n        excerpt: 'An in-depth analysis of how constitutional rights are evolving in response to digital surveillance and data privacy concerns in modern society.',\n        image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=800&h=400&fit=crop',\n        author: {\n            name: 'Sarah Johnson',\n            avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=100&h=100&fit=crop&crop=face',\n            university: 'Harvard Law School'\n        },\n        category: 'Constitutional Law',\n        publishedAt: '2024-01-15',\n        readingTime: 8,\n        views: 1250,\n        tags: [\n            'privacy',\n            'digital rights',\n            'constitution'\n        ]\n    },\n    {\n        id: '2',\n        title: 'International Trade Law: Navigating Global Commerce',\n        excerpt: 'Understanding the complexities of international trade agreements and their impact on global economic relationships.',\n        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop',\n        author: {\n            name: 'Michael Chen',\n            avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',\n            university: 'Stanford Law School'\n        },\n        category: 'International Law',\n        publishedAt: '2024-01-12',\n        readingTime: 12,\n        views: 890,\n        tags: [\n            'trade',\n            'international',\n            'commerce'\n        ]\n    },\n    {\n        id: '3',\n        title: 'Criminal Justice Reform: A Path Forward',\n        excerpt: 'Examining current criminal justice policies and proposing evidence-based reforms for a more equitable system.',\n        image: 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=800&h=400&fit=crop',\n        author: {\n            name: 'Emily Rodriguez',\n            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',\n            university: 'Yale Law School'\n        },\n        category: 'Criminal Law',\n        publishedAt: '2024-01-10',\n        readingTime: 10,\n        views: 1100,\n        tags: [\n            'reform',\n            'justice',\n            'policy'\n        ]\n    },\n    {\n        id: '4',\n        title: 'Environmental Law and Climate Change',\n        excerpt: 'Exploring the legal frameworks needed to address climate change and environmental protection in the 21st century.',\n        image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?w=800&h=400&fit=crop',\n        author: {\n            name: 'David Kim',\n            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',\n            university: 'Columbia Law School'\n        },\n        category: 'Environmental Law',\n        publishedAt: '2024-01-08',\n        readingTime: 15,\n        views: 750,\n        tags: [\n            'environment',\n            'climate',\n            'sustainability'\n        ]\n    },\n    {\n        id: '5',\n        title: 'Corporate Governance in Modern Business',\n        excerpt: 'Analyzing the evolving landscape of corporate governance and its impact on business ethics and accountability.',\n        image: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=800&h=400&fit=crop',\n        author: {\n            name: 'Lisa Wang',\n            avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=100&h=100&fit=crop&crop=face',\n            university: 'NYU Law School'\n        },\n        category: 'Corporate Law',\n        publishedAt: '2024-01-05',\n        readingTime: 9,\n        views: 920,\n        tags: [\n            'corporate',\n            'governance',\n            'business'\n        ]\n    },\n    {\n        id: '6',\n        title: 'Human Rights in the Digital Era',\n        excerpt: 'Examining how traditional human rights concepts apply to digital spaces and emerging technologies.',\n        image: 'https://images.unsplash.com/photo-*************-d95e436ab8d6?w=800&h=400&fit=crop',\n        author: {\n            name: 'Ahmed Hassan',\n            avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',\n            university: 'Georgetown Law'\n        },\n        category: 'Human Rights',\n        publishedAt: '2024-01-03',\n        readingTime: 11,\n        views: 1050,\n        tags: [\n            'human rights',\n            'digital',\n            'technology'\n        ]\n    }\n];\nfunction ArticlesPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [filteredArticles, setFilteredArticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockArticles);\n    const [isSearchDropdownOpen, setIsSearchDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSearch = (filters)=>{\n        console.log('Search filters:', filters);\n    // Implement filtering logic here\n    // For now, just log the filters\n    };\n    const handleDropdownToggle = (isOpen)=>{\n        setIsSearchDropdownOpen(isOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_reading_progress__WEBPACK_IMPORTED_MODULE_4__.ReadingProgress, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_navigation__WEBPACK_IMPORTED_MODULE_2__.BlogNavigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"gradient-text\",\n                                                    children: t('Articles.title')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                                                children: t('Articles.subtitle')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    delay: 200,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-4xl mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search__WEBPACK_IMPORTED_MODULE_5__.SearchComponent, {\n                                            onSearch: handleSearch,\n                                            placeholder: t('Articles.searchPlaceholder'),\n                                            showFilters: true,\n                                            onDropdownToggle: handleDropdownToggle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 \".concat(isSearchDropdownOpen ? 'blur-sm' : '', \" transition-all duration-300\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 space-y-4 sm:space-y-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center sm:space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl sm:text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"All Articles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            filteredArticles.length,\n                                                            \" articles found\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setViewMode('grid'),\n                                                        className: \"p-2 rounded-lg transition-colors duration-200 \".concat(viewMode === 'grid' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'),\n                                                        title: \"Grid View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setViewMode('list'),\n                                                        className: \"p-2 rounded-lg transition-colors duration-200 \".concat(viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'),\n                                                        title: \"List View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6',\n                                        children: [\n                                            ...Array(6)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.ArticleCardSkeleton, {}, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, void 0))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.StaggeredList, {\n                                        delay: 100,\n                                        className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6',\n                                        children: filteredArticles.map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_article_card__WEBPACK_IMPORTED_MODULE_6__.ArticleCard, {\n                                                article: article,\n                                                variant: viewMode === 'list' ? 'compact' : 'default',\n                                                showBookmark: true,\n                                                showShare: true,\n                                                locale: \"en\"\n                                            }, article.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    delay: 600,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"btn-primary px-8 py-3 rounded-xl font-semibold text-white hover-lift\",\n                                            children: t('Articles.loadMore')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isSearchDropdownOpen ? 'blur-sm' : '', \" transition-all duration-300\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_footer__WEBPACK_IMPORTED_MODULE_3__.BlogFooter, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(ArticlesPage, \"Hy6h2/p8MS7jPn+2zHjVY+ll/1s=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations\n    ];\n});\n_c = ArticlesPage;\nvar _c;\n$RefreshReg$(_c, \"ArticlesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/articles/page.tsx\n"));

/***/ })

});