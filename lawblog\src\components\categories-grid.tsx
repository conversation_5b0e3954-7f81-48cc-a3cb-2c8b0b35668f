'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { BookOpen, ArrowRight } from 'lucide-react'
import { Category } from '@/lib/blog-service'

interface CategoriesGridProps {
  categories: Category[]
}

export function CategoriesGrid({ categories }: CategoriesGridProps) {
  const params = useParams()
  const locale = params.locale as string

  const getCategoryIcon = (categoryName: string) => {
    const icons: Record<string, string> = {
      'Constitutional Law': '⚖️',
      'Criminal Law': '🔒',
      'Corporate Law': '🏢',
      'Environmental Law': '🌱',
      'International Law': '🌍',
      'Civil Rights': '✊',
      'Legal Technology': '💻',
    }
    return icons[categoryName] || '📚'
  }

  const getCategoryDescription = (categoryName: string) => {
    const descriptions: Record<string, string> = {
      'Constitutional Law': 'Explore fundamental principles of constitutional interpretation, rights, and governmental powers.',
      'Criminal Law': 'Dive into criminal justice, procedures, and the evolving landscape of criminal law.',
      'Corporate Law': 'Understand business law, corporate governance, and commercial legal frameworks.',
      'Environmental Law': 'Learn about environmental regulations, climate law, and sustainability legal issues.',
      'International Law': 'Discover global legal frameworks, treaties, and international legal cooperation.',
      'Civil Rights': 'Examine human rights, social justice, and civil liberties in legal practice.',
      'Legal Technology': 'Explore how technology is transforming legal practice and the justice system.',
    }
    return descriptions[categoryName] || 'Discover insightful articles in this legal specialization.'
  }

  return (
    <div className="space-y-8">
      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
        {categories.map((category) => (
          <Link
            key={category.id}
            href={`/${locale}/categories/${category.slug}`}
            className="group"
          >
            <div className="bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 h-full">
              {/* Header */}
              <div 
                className="h-24 relative overflow-hidden"
                style={{ backgroundColor: category.color }}
              >
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-4xl">{getCategoryIcon(category.name)}</span>
                </div>
                <div className="absolute top-4 right-4">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full px-3 py-1">
                    <span className="text-white text-sm font-medium">
                      {category.postCount} articles
                    </span>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                  {category.name}
                </h3>
                
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {category.description || getCategoryDescription(category.name)}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <BookOpen className="w-4 h-4" />
                    <span>{category.postCount} articles</span>
                  </div>
                  
                  <div className="flex items-center space-x-1 text-blue-600 group-hover:text-blue-700 font-medium text-sm">
                    <span>Explore</span>
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* No Categories */}
      {categories.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📚</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No categories yet</h3>
          <p className="text-gray-600">
            We're organizing our content into categories. Check back soon for organized legal topics!
          </p>
        </div>
      )}

      {/* Featured Categories */}
      {categories.length > 0 && (
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">Explore Legal Specializations</h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Our articles cover a wide range of legal topics, from constitutional law to emerging legal technology. 
            Find content that matches your academic interests and career goals.
          </p>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
            {categories.slice(0, 4).map((category) => (
              <div key={category.id} className="text-center">
                <div className="text-2xl mb-2">{getCategoryIcon(category.name)}</div>
                <div className="text-sm font-medium">{category.name}</div>
                <div className="text-xs text-blue-200">{category.postCount} articles</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
