'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import { MapPin, GraduationCap, BookOpen, Eye, Calendar, ExternalLink } from 'lucide-react'
import { Writer } from '@/lib/blog-service'

interface WritersGridProps {
  writers: Writer[]
}

export function WritersGrid({ writers }: WritersGridProps) {
  const params = useParams()
  const locale = params.locale as string

  const formatJoinDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    })
  }

  const createWriterSlug = (name: string) => {
    return name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
  }

  return (
    <div className="space-y-8">
      {/* Writers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
        {writers.map((writer) => (
          <div
            key={writer.id}
            className="bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 group"
          >
            {/* Profile Header */}
            <div className="relative h-32 bg-gradient-to-br from-blue-500 to-purple-600">
              <div className="absolute inset-0 bg-black/20"></div>
              <div className="absolute bottom-4 left-6">
                <div className="flex items-center space-x-3">
                  {writer.avatarUrl ? (
                    <div className="relative w-16 h-16 rounded-full overflow-hidden border-4 border-white shadow-lg">
                      <Image
                        src={writer.avatarUrl}
                        alt={writer.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-16 h-16 rounded-full bg-white border-4 border-white shadow-lg flex items-center justify-center">
                      <span className="text-2xl font-bold text-gray-600">
                        {writer.name.charAt(0)}
                      </span>
                    </div>
                  )}
                  <div>
                    <h3 className="text-white font-bold text-lg">{writer.name}</h3>
                    {writer.specialization && (
                      <p className="text-white/90 text-sm">{writer.specialization}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 pt-4">
              {/* University Info */}
              <div className="space-y-2 mb-4">
                {writer.university && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <GraduationCap className="w-4 h-4" />
                    <span>{writer.university}</span>
                    {writer.graduationYear && (
                      <span className="text-gray-400">• Class of {writer.graduationYear}</span>
                    )}
                  </div>
                )}
                
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Calendar className="w-4 h-4" />
                  <span>Joined {formatJoinDate(writer.joinedDate)}</span>
                </div>
              </div>

              {/* Bio */}
              {writer.bio && (
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {writer.bio}
                </p>
              )}

              {/* Stats */}
              <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-blue-600 mb-1">
                    <BookOpen className="w-4 h-4" />
                    <span className="font-bold text-lg">{writer.totalPosts}</span>
                  </div>
                  <span className="text-xs text-gray-500">Articles</span>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-purple-600 mb-1">
                    <Eye className="w-4 h-4" />
                    <span className="font-bold text-lg">{writer.totalViews.toLocaleString()}</span>
                  </div>
                  <span className="text-xs text-gray-500">Views</span>
                </div>
              </div>

              {/* Social Links */}
              {writer.socialLinks && Object.keys(writer.socialLinks).length > 0 && (
                <div className="flex items-center space-x-2 mb-4">
                  {Object.entries(writer.socialLinks).map(([platform, url]) => (
                    <a
                      key={platform}
                      href={url as string}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                  ))}
                </div>
              )}

              {/* Action Button */}
              <Link
                href={`/${locale}/writers/${createWriterSlug(writer.name)}`}
                className="block w-full text-center bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 transform group-hover:scale-105"
              >
                View Profile & Articles
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* No Writers */}
      {writers.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">👩‍🎓</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No writers yet</h3>
          <p className="text-gray-600">
            We're currently building our team of talented law student writers. Check back soon!
          </p>
        </div>
      )}

      {/* Call to Action */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-center text-white">
        <h3 className="text-2xl font-bold mb-4">Interested in Writing for አፈሳታ?</h3>
        <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
          We're always looking for talented law students to join our writing community. 
          Share your insights and help shape the future of legal education.
        </p>
        <div className="text-sm text-blue-100">
          Contact our admin team to learn more about becoming a writer.
        </div>
      </div>
    </div>
  )
}
