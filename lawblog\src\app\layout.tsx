import type { Metadata } from "next";
import { Inter, Playfair_Display, Noto_Sans_Ethiopic } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
  display: "swap",
});

const notoSansEthiopic = Noto_Sans_Ethiopic({
  subsets: ["ethiopic"],
  variable: "--font-ethiopic",
  display: "swap",
});

export const metadata: Metadata = {
  title: "አፈርሳታ - Legal Blog Platform",
  description: "Where legal minds share stories. Discover insightful articles written by talented law students from top universities around the world.",
  keywords: ["legal blog", "law students", "legal education", "legal articles", "law school"],
  authors: [{ name: "አፈርሳታ Team" }],
  creator: "አፈርሳታ",
  publisher: "አፈርሳታ",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    title: "አፈርሳታ - Legal Blog Platform",
    description: "Where legal minds share stories. Discover insightful articles written by talented law students.",
    url: "/",
    siteName: "አፈርሳታ",
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "አፈርሳታ - Legal Blog Platform",
    description: "Where legal minds share stories. Discover insightful articles written by talented law students.",
    creator: "@afersata",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

// Root layout for non-localized routes (admin, api, etc.)
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html className={`${inter.variable} ${playfair.variable} ${notoSansEthiopic.variable}`} suppressHydrationWarning>
      <body className="font-inter antialiased">
        {children}
      </body>
    </html>
  );
}
