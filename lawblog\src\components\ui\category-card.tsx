'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { BookOpen, TrendingUp, Users, ArrowRight, Scale, Globe, Building, Heart, Shield, Gavel } from 'lucide-react';
import { cn } from '@/lib/utils';
import { FadeIn } from './page-transition';

interface Category {
  id: string;
  name: string;
  description: string;
  articlesCount: number;
  writersCount: number;
  totalViews: number;
  color: string;
  icon?: string;
  featured?: boolean;
  trending?: boolean;
}

interface CategoryCardProps {
  category: Category;
  variant?: 'default' | 'compact' | 'featured';
  className?: string;
  locale?: string;
}

const categoryIcons = {
  'constitutional': Scale,
  'criminal': Shield,
  'civil': Gavel,
  'commercial': Building,
  'international': Globe,
  'environmental': Heart,
  'corporate': Building,
  'human-rights': Heart,
  'default': BookOpen
};

export function CategoryCard({ 
  category, 
  variant = 'default',
  className,
  locale = 'en'
}: CategoryCardProps) {
  const t = useTranslations();
  const [isHovered, setIsHovered] = useState(false);

  const IconComponent = categoryIcons[category.icon as keyof typeof categoryIcons] || categoryIcons.default;
  const isFeatured = variant === 'featured';
  const isCompact = variant === 'compact';

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  if (isCompact) {
    return (
      <FadeIn>
        <Link href={`/${locale}/categories/${category.id}`}>
          <div 
            className={cn(
              "glass-card p-4 transition-all duration-300 hover-lift card-hover cursor-pointer",
              className
            )}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <div className="flex items-center space-x-4">
              <div 
                className="flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center"
                style={{ backgroundColor: `${category.color}20` }}
              >
                <IconComponent 
                  className="w-6 h-6" 
                  style={{ color: category.color }}
                />
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 truncate">
                  {category.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {category.articlesCount} {t('Categories.articles')}
                </p>
              </div>
              
              <ArrowRight className={cn(
                "w-4 h-4 text-gray-400 transition-all duration-300",
                isHovered && "translate-x-1 text-blue-600 dark:text-blue-400"
              )} />
            </div>
          </div>
        </Link>
      </FadeIn>
    );
  }

  return (
    <FadeIn>
      <Link href={`/${locale}/categories/${category.id}`}>
        <article 
          className={cn(
            "glass-card overflow-hidden transition-all duration-300 hover-lift card-hover cursor-pointer",
            isFeatured ? "p-8" : "p-6",
            className
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Header */}
          <div className="text-center mb-6">
            <div className="relative inline-block mb-4">
              <div 
                className={cn(
                  "rounded-2xl flex items-center justify-center transition-all duration-300 mx-auto",
                  isFeatured ? "w-20 h-20" : "w-16 h-16",
                  isHovered && "scale-110"
                )}
                style={{ backgroundColor: `${category.color}20` }}
              >
                <IconComponent 
                  className={cn(
                    isFeatured ? "w-10 h-10" : "w-8 h-8"
                  )} 
                  style={{ color: category.color }}
                />
              </div>
              
              {category.trending && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-3 h-3 text-white" />
                </div>
              )}
              
              {category.featured && (
                <div className="absolute -top-2 -left-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                  <span className="text-xs">⭐</span>
                </div>
              )}
            </div>

            <h2 className={cn(
              "font-bold text-gray-900 dark:text-white mb-2 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200",
              isFeatured ? "text-2xl" : "text-xl"
            )}>
              {category.name}
            </h2>
          </div>

          {/* Description */}
          <p className={cn(
            "text-gray-600 dark:text-gray-300 text-center mb-6",
            isFeatured ? "text-base" : "text-sm line-clamp-3"
          )}>
            {category.description}
          </p>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {category.articlesCount}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {t('Categories.articles')}
              </div>
            </div>
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {category.writersCount}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {t('Writers.writers')}
              </div>
            </div>
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {formatNumber(category.totalViews)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Views
              </div>
            </div>
          </div>

          {/* Action */}
          <div className="text-center">
            <div className="inline-flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200">
              <span>{t('Categories.explore')}</span>
              <ArrowRight className={cn(
                "w-4 h-4 transition-transform duration-300",
                isHovered && "translate-x-1"
              )} />
            </div>
          </div>
        </article>
      </Link>
    </FadeIn>
  );
}

// Component for category statistics
interface CategoryStatsProps {
  categories: Category[];
  className?: string;
}

export function CategoryStats({ categories, className }: CategoryStatsProps) {
  const t = useTranslations();

  const totalArticles = categories.reduce((sum, cat) => sum + cat.articlesCount, 0);
  const totalWriters = categories.reduce((sum, cat) => sum + cat.writersCount, 0);
  const totalViews = categories.reduce((sum, cat) => sum + cat.totalViews, 0);
  const totalCategories = categories.length;

  const stats = [
    { label: 'Categories', value: totalCategories, icon: BookOpen },
    { label: 'Articles', value: totalArticles, icon: BookOpen },
    { label: 'Writers', value: totalWriters, icon: Users },
    { label: 'Total Views', value: totalViews > 1000 ? `${(totalViews / 1000).toFixed(1)}K` : totalViews, icon: TrendingUp }
  ];

  return (
    <div className={cn("grid grid-cols-2 lg:grid-cols-4 gap-6", className)}>
      {stats.map((stat, index) => (
        <FadeIn key={index} delay={index * 100}>
          <div className="glass-card p-6 text-center hover-lift">
            <stat.icon className="w-8 h-8 mx-auto mb-3 text-blue-600 dark:text-blue-400" />
            <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
              {stat.value}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {stat.label}
            </div>
          </div>
        </FadeIn>
      ))}
    </div>
  );
}

// Component for trending categories
interface TrendingCategoriesProps {
  categories: Category[];
  maxCategories?: number;
  locale?: string;
  className?: string;
}

export function TrendingCategories({ 
  categories, 
  maxCategories = 5,
  locale = 'en',
  className 
}: TrendingCategoriesProps) {
  const t = useTranslations();

  const trendingCategories = categories
    .filter(cat => cat.trending)
    .slice(0, maxCategories);

  if (trendingCategories.length === 0) {
    return null;
  }

  return (
    <div className={cn("glass-card p-6", className)}>
      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
        <TrendingUp className="w-5 h-5 mr-2 text-red-500" />
        {t('Categories.trending')}
      </h3>
      
      <div className="space-y-3">
        {trendingCategories.map((category, index) => (
          <FadeIn key={category.id} delay={index * 100}>
            <CategoryCard 
              category={category}
              variant="compact"
              locale={locale}
            />
          </FadeIn>
        ))}
      </div>
    </div>
  );
}
