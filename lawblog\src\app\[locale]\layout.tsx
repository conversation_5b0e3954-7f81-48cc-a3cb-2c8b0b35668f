import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Playfair_Display, Noto_Sans_Ethiopic } from "next/font/google";
import "../globals.css";
import { Providers } from "@/components/providers";
import { ThemeProvider } from "@/components/theme-provider";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { Toaster } from "react-hot-toast";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
  display: "swap",
});

const notoSansEthiopic = Noto_Sans_Ethiopic({
  subsets: ["ethiopic"],
  variable: "--font-ethiopic",
  display: "swap",
});

export const metadata: Metadata = {
  title: "አፈርሳታ - Legal Blog Platform",
  description: "Where legal minds share stories. Discover insightful articles written by talented law students from top universities around the world.",
  keywords: ["legal blog", "law students", "legal education", "legal articles", "law school", "አፈርሳታ"],
  authors: [{ name: "አፈርሳታ Team" }],
  creator: "አፈርሳታ",
  publisher: "አፈርሳታ",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"),
  openGraph: {
    type: "website",
    locale: "en_US",
    url: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
    title: "አፈርሳታ - Legal Blog Platform",
    description: "Where legal minds share stories. Discover insightful articles written by talented law students.",
    siteName: "አፈርሳታ",
  },
  twitter: {
    card: "summary_large_image",
    title: "አፈርሳታ - Legal Blog Platform",
    description: "Where legal minds share stories. Discover insightful articles written by talented law students.",
    creator: "@afersata",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  const { locale } = await params;

  // Validate locale
  const locales = ['en', 'am'];
  if (!locales.includes(locale)) {
    notFound();
  }

  // Get messages for the locale
  const messages = await getMessages();

  return (
    <html lang={locale} className={`${inter.variable} ${playfair.variable} ${notoSansEthiopic.variable} dark`} suppressHydrationWarning>
      <body className="min-h-screen bg-white dark:bg-gray-900 font-inter antialiased transition-colors duration-300">
        <NextIntlClientProvider messages={messages}>
          <ThemeProvider>
            <Providers>
              {children}
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  className: 'dark:bg-gray-800 dark:text-white bg-white text-gray-900',
                  style: {
                    borderRadius: "12px",
                    padding: "16px",
                    fontSize: "14px",
                    fontWeight: "500",
                  },
                }}
              />
            </Providers>
          </ThemeProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
