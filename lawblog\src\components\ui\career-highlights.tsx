'use client';

import { GraduationCap, Award, BookOpen, Users, TrendingUp, FileText, Download } from 'lucide-react';
import { FadeIn } from './page-transition';

interface CareerHighlightsProps {
  className?: string;
}

export function CareerHighlights({ className = '' }: CareerHighlightsProps) {
  const highlights = [
    {
      icon: GraduationCap,
      title: 'Academic Excellence',
      description: 'Top law students from prestigious universities',
      stats: '3.7+ Average GPA'
    },
    {
      icon: Award,
      title: 'Professional Recognition',
      description: 'Law review editors, moot court champions, and award winners',
      stats: '85% on Law Review'
    },
    {
      icon: BookOpen,
      title: 'Published Authors',
      description: 'Students with published articles and legal commentary',
      stats: '500+ Publications'
    },
    {
      icon: Users,
      title: 'Career Ready',
      description: 'Internship experience at top firms and organizations',
      stats: '90% Placement Rate'
    }
  ];

  return (
    <section className={`py-16 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Why Recruit From Our Platform?
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Connect with the next generation of legal talent. Our writers are carefully vetted law students 
              with proven academic excellence and professional potential.
            </p>
          </div>
        </FadeIn>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {highlights.map((highlight, index) => (
            <FadeIn key={index} delay={index * 150}>
              <div className="glass-card p-6 text-center hover-lift">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                  <highlight.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  {highlight.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-3 text-sm">
                  {highlight.description}
                </p>
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {highlight.stats}
                </div>
              </div>
            </FadeIn>
          ))}
        </div>

        {/* Call to Action for Recruiters */}
        <FadeIn delay={600}>
          <div className="mt-16 text-center">
            <div className="glass-card p-8 max-w-4xl mx-auto">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                For Legal Recruiters & Employers
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Access detailed profiles, academic records, writing samples, and professional portfolios. 
                Find the perfect candidates for your internship programs, entry-level positions, and clerkships.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="btn-primary px-8 py-3 rounded-xl font-semibold text-white hover-lift flex items-center space-x-2">
                  <Users className="w-5 h-5" />
                  <span>Browse All Profiles</span>
                </button>
                <button className="glass border border-gray-200 dark:border-gray-700 px-8 py-3 rounded-xl font-semibold text-gray-900 dark:text-white hover-lift flex items-center space-x-2">
                  <Download className="w-5 h-5" />
                  <span>Download Talent Guide</span>
                </button>
              </div>
            </div>
          </div>
        </FadeIn>
      </div>
    </section>
  );
}

// Component for individual career metrics
interface CareerMetricsProps {
  writer: {
    gpa?: string;
    lawReview?: boolean;
    mootCourt?: boolean;
    internships?: string[];
    publications?: string[];
    awards?: string[];
  };
  className?: string;
}

export function CareerMetrics({ writer, className = '' }: CareerMetricsProps) {
  const metrics = [
    {
      label: 'GPA',
      value: writer.gpa || 'N/A',
      icon: GraduationCap,
      color: 'text-blue-600 dark:text-blue-400'
    },
    {
      label: 'Publications',
      value: writer.publications?.length || 0,
      icon: BookOpen,
      color: 'text-green-600 dark:text-green-400'
    },
    {
      label: 'Internships',
      value: writer.internships?.length || 0,
      icon: Users,
      color: 'text-purple-600 dark:text-purple-400'
    },
    {
      label: 'Awards',
      value: writer.awards?.length || 0,
      icon: Award,
      color: 'text-yellow-600 dark:text-yellow-400'
    }
  ];

  return (
    <div className={`grid grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {metrics.map((metric, index) => (
        <div key={index} className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <metric.icon className={`w-6 h-6 mx-auto mb-2 ${metric.color}`} />
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {metric.value}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {metric.label}
          </div>
        </div>
      ))}
    </div>
  );
}

// Component for resume/portfolio actions
interface ResumeActionsProps {
  resumeUrl?: string;
  portfolioUrl?: string;
  className?: string;
}

export function ResumeActions({ resumeUrl, portfolioUrl, className = '' }: ResumeActionsProps) {
  if (!resumeUrl && !portfolioUrl) {
    return null;
  }

  return (
    <div className={`flex flex-col sm:flex-row gap-3 ${className}`}>
      {resumeUrl && (
        <a
          href={resumeUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-semibold hover-lift transition-all duration-200"
        >
          <Download className="w-4 h-4" />
          <span>Download Resume</span>
        </a>
      )}
      {portfolioUrl && (
        <a
          href={portfolioUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl font-semibold hover-lift transition-all duration-200"
        >
          <FileText className="w-4 h-4" />
          <span>View Portfolio</span>
        </a>
      )}
    </div>
  );
}

// Component for academic credentials display
interface AcademicCredentialsProps {
  writer: {
    university: string;
    yearOfStudy?: string;
    gpa?: string;
    lawReview?: boolean;
    mootCourt?: boolean;
    specialization: string;
  };
  className?: string;
}

export function AcademicCredentials({ writer, className = '' }: AcademicCredentialsProps) {
  return (
    <div className={`space-y-3 ${className}`}>
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {writer.university}
        </h3>
        <p className="text-blue-600 dark:text-blue-400 font-medium">
          {writer.specialization}
        </p>
        {writer.yearOfStudy && (
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {writer.yearOfStudy}
          </p>
        )}
      </div>

      <div className="flex justify-center space-x-4 text-sm">
        {writer.gpa && (
          <div className="flex items-center space-x-1">
            <GraduationCap className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <span className="text-gray-600 dark:text-gray-400">GPA: {writer.gpa}</span>
          </div>
        )}
        {writer.lawReview && (
          <div className="flex items-center space-x-1">
            <BookOpen className="w-4 h-4 text-purple-600 dark:text-purple-400" />
            <span className="text-gray-600 dark:text-gray-400">Law Review</span>
          </div>
        )}
        {writer.mootCourt && (
          <div className="flex items-center space-x-1">
            <Award className="w-4 h-4 text-green-600 dark:text-green-400" />
            <span className="text-gray-600 dark:text-gray-400">Moot Court</span>
          </div>
        )}
      </div>
    </div>
  );
}
