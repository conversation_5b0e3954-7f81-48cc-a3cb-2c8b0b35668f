import { SimpleNavigation } from '@/components/simple-navigation'
import { WritersGridSimple } from '@/components/writers-grid-simple'
import { SimpleFooter } from '@/components/simple-footer'
import { getWriters } from '@/lib/blog-service'

export default async function WritersPage() {
  try {
    const writers = await getWriters()

    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <SimpleNavigation />
        <main className="pt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Header */}
            <div className="text-center mb-12">
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                Meet Our Writers
              </h1>
              <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Discover the talented law students behind our insightful articles. Learn about their backgrounds, specializations, and academic journeys.
              </p>
            </div>

            <WritersGridSimple writers={writers} />
          </div>
        </main>
        <SimpleFooter />
      </div>
    )
  } catch (error) {
    console.error('Error loading writers:', error)
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <SimpleNavigation />
        <main className="pt-16 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Writers</h1>
            <p className="text-gray-600 dark:text-gray-400">Loading writers...</p>
          </div>
        </main>
        <SimpleFooter />
      </div>
    )
  }
}
