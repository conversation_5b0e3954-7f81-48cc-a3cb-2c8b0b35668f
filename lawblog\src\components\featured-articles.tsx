'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { Clock, User, Eye, Calendar, ArrowRight } from 'lucide-react'
import { Post } from '@/lib/blog-service'

interface FeaturedArticlesProps {
  posts: Post[]
}

export function FeaturedArticles({ posts }: FeaturedArticlesProps) {
  const params = useParams()
  const locale = params.locale as string
  const t = useTranslations()

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (posts.length === 0) {
    return null
  }

  return (
    <section className="py-16 sm:py-24 bg-gray-50 dark:bg-gray-800/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12 sm:mb-16">
          <div className="inline-flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/50 px-4 py-2 rounded-full text-sm font-medium text-blue-600 dark:text-blue-400 mb-4">
            <span>{t('common.featured')}</span>
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            {t('homepage.featuredArticles')}
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            {t('homepage.featuredSubtitle')}
          </p>
        </div>

        {/* Featured Articles Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
          {/* Main Featured Article */}
          {posts[0] && (
            <article className="lg:col-span-1">
              <div className="bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 group h-full">
                {/* Featured Image */}
                {posts[0].featuredImage ? (
                  <div className="relative h-64 sm:h-80 overflow-hidden">
                    <Image
                      src={posts[0].featuredImage}
                      alt={posts[0].title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    {posts[0].category && (
                      <div className="absolute top-4 left-4">
                        <span
                          className="px-3 py-1 text-xs font-semibold text-white rounded-full"
                          style={{ backgroundColor: posts[0].category.color }}
                        >
                          {posts[0].category.name}
                        </span>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="h-64 sm:h-80 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center relative">
                    <div className="text-6xl text-blue-200 dark:text-gray-600">📚</div>
                    {posts[0].category && (
                      <div className="absolute top-4 left-4">
                        <span
                          className="px-3 py-1 text-xs font-semibold text-white rounded-full"
                          style={{ backgroundColor: posts[0].category.color }}
                        >
                          {posts[0].category.name}
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {/* Content */}
                <div className="p-6 sm:p-8">
                  {/* Meta Info */}
                  <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-4">
                    {posts[0].writer && (
                      <div className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{posts[0].writer.name}</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{posts[0].readingTime} {t('homepage.readingTime')}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Eye className="w-4 h-4" />
                      <span>{posts[0].viewCount}</span>
                    </div>
                  </div>

                  {/* Title */}
                  <Link href={`/${locale}/articles/${posts[0].slug}`}>
                    <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
                      {posts[0].title}
                    </h3>
                  </Link>

                  {/* Excerpt */}
                  {posts[0].excerpt && (
                    <p className="text-gray-600 dark:text-gray-400 mb-6 line-clamp-3 text-lg">
                      {posts[0].excerpt}
                    </p>
                  )}

                  {/* Footer */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(posts[0].publishedAt || posts[0].createdAt)}</span>
                    </div>
                    
                    <Link
                      href={`/${locale}/articles/${posts[0].slug}`}
                      className="inline-flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors group"
                    >
                      <span>{t('common.readMore')}</span>
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </div>
              </div>
            </article>
          )}

          {/* Secondary Featured Articles */}
          <div className="lg:col-span-1 space-y-6">
            {posts.slice(1, 3).map((post) => (
              <article
                key={post.id}
                className="bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 group"
              >
                <div className="flex flex-col sm:flex-row">
                  {/* Featured Image */}
                  {post.featuredImage ? (
                    <div className="relative w-full sm:w-48 h-48 sm:h-32 flex-shrink-0 overflow-hidden">
                      <Image
                        src={post.featuredImage}
                        alt={post.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      {post.category && (
                        <div className="absolute top-2 left-2">
                          <span
                            className="px-2 py-1 text-xs font-semibold text-white rounded"
                            style={{ backgroundColor: post.category.color }}
                          >
                            {post.category.name}
                          </span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="w-full sm:w-48 h-48 sm:h-32 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center flex-shrink-0 relative">
                      <div className="text-4xl text-blue-200 dark:text-gray-600">📚</div>
                      {post.category && (
                        <div className="absolute top-2 left-2">
                          <span
                            className="px-2 py-1 text-xs font-semibold text-white rounded"
                            style={{ backgroundColor: post.category.color }}
                          >
                            {post.category.name}
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Content */}
                  <div className="p-4 sm:p-6 flex-1">
                    {/* Meta Info */}
                    <div className="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400 mb-2">
                      {post.writer && (
                        <div className="flex items-center space-x-1">
                          <User className="w-3 h-3" />
                          <span>{post.writer.name}</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{post.readingTime} {t('homepage.readingTime')}</span>
                      </div>
                    </div>

                    {/* Title */}
                    <Link href={`/${locale}/articles/${post.slug}`}>
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
                        {post.title}
                      </h3>
                    </Link>

                    {/* Excerpt */}
                    {post.excerpt && (
                      <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-2 mb-3">
                        {post.excerpt}
                      </p>
                    )}

                    {/* Date */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(post.publishedAt || post.createdAt)}</span>
                      </div>
                      
                      <Link
                        href={`/${locale}/articles/${post.slug}`}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm transition-colors"
                      >
                        {t('common.readMore')} →
                      </Link>
                    </div>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Link
            href={`/${locale}/articles`}
            className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105"
          >
            <span>{t('common.viewAll')}</span>
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>
      </div>
    </section>
  )
}
