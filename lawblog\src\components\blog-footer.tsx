'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { BookOpen, Mail, Globe, Heart } from 'lucide-react'

export function BlogFooter() {
  const params = useParams()
  const locale = params.locale as string
  const t = useTranslations()

  const currentYear = new Date().getFullYear()

  const footerLinks = {
    explore: [
      { name: t('navigation.articles'), href: `/${locale}/articles` },
      { name: t('navigation.writers'), href: `/${locale}/writers` },
      { name: t('navigation.categories'), href: `/${locale}/categories` },
    ],
    legal: [
      { name: 'Constitutional Law', href: `/${locale}/categories/constitutional-law` },
      { name: 'Criminal Law', href: `/${locale}/categories/criminal-law` },
      { name: 'Corporate Law', href: `/${locale}/categories/corporate-law` },
      { name: 'Environmental Law', href: `/${locale}/categories/environmental-law` },
    ],
    about: [
      { name: `${t('footer.about')} ${t('common.appName')}`, href: `/${locale}/about` },
      { name: t('navigation.contact'), href: `/${locale}/contact` },
      { name: 'Privacy Policy', href: `/${locale}/privacy` },
      { name: 'Terms of Service', href: `/${locale}/terms` },
    ]
  }

  return (
    <footer className="bg-gray-900 dark:bg-gray-950 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold font-ethiopic">{t('common.appName')}</span>
            </div>
            <p className="text-gray-400 dark:text-gray-500 mb-6 leading-relaxed">
              {t('footer.description')}
            </p>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1 text-sm text-gray-400 dark:text-gray-500">
                <Globe className="w-4 h-4" />
                <span>{t('footer.availableIn')}</span>
              </div>
            </div>
          </div>

          {/* Explore */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.explore')}</h3>
            <ul className="space-y-3">
              {footerLinks.explore.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 dark:text-gray-500 hover:text-white dark:hover:text-gray-300 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Topics */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.legalTopics')}</h3>
            <ul className="space-y-3">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 dark:text-gray-500 hover:text-white dark:hover:text-gray-300 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* About */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.about')}</h3>
            <ul className="space-y-3">
              {footerLinks.about.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 dark:text-gray-500 hover:text-white dark:hover:text-gray-300 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>

            {/* Contact */}
            <div className="mt-6 pt-6 border-t border-gray-800 dark:border-gray-700">
              <div className="flex items-center space-x-2 text-gray-400 dark:text-gray-500">
                <Mail className="w-4 h-4" />
                <span className="text-sm"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-gray-800 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-1 text-gray-400 dark:text-gray-500 text-sm">
              <span>{t('footer.copyright')}</span>
            </div>

            <div className="flex items-center space-x-6 text-sm text-gray-400 dark:text-gray-500">
              <span>{t('footer.poweredBy')}</span>
              <div className="flex items-center space-x-2">
                <Link
                  href="/en"
                  className={`px-2 py-1 rounded transition-colors ${
                    locale === 'en' ? 'bg-blue-600 text-white' : 'hover:text-white dark:hover:text-gray-300'
                  }`}
                >
                  English
                </Link>
                <Link
                  href="/am"
                  className={`px-2 py-1 rounded transition-colors font-ethiopic ${
                    locale === 'am' ? 'bg-blue-600 text-white' : 'hover:text-white dark:hover:text-gray-300'
                  }`}
                >
                  አማርኛ
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
