'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { MapPin, BookOpen, Eye, Award, ExternalLink, Mail, Linkedin, Twitter, FileText, Download, GraduationCap, Scale, Trophy, Globe } from 'lucide-react';
import { cn } from '@/lib/utils';
import { FadeIn } from './page-transition';

interface Writer {
  id: string;
  name: string;
  avatar?: string;
  bio: string;
  university: string;
  specialization: string;
  location?: string;
  articlesCount: number;
  totalViews: number;
  joinedDate: string;
  achievements?: string[];
  socialLinks?: {
    email?: string;
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
  featured?: boolean;
  // Resume/Career focused fields
  yearOfStudy?: string;
  gpa?: string;
  lawReview?: boolean;
  mootCourt?: boolean;
  internships?: string[];
  publications?: string[];
  awards?: string[];
  barAdmissions?: string[];
  languages?: string[];
  interests?: string[];
  careerGoals?: string;
  resumeUrl?: string;
  portfolioUrl?: string;
}

interface WriterCardProps {
  writer: Writer;
  variant?: 'default' | 'compact' | 'detailed' | 'minimal';
  className?: string;
  locale?: string;
}

export function WriterCard({ 
  writer, 
  variant = 'default',
  className,
  locale = 'en'
}: WriterCardProps) {
  const t = useTranslations();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'short'
    }).format(date);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2);
  };

  if (variant === 'compact') {
    return (
      <FadeIn>
        <div 
          className={cn(
            "glass-card p-4 transition-all duration-300 hover-lift card-hover",
            className
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="flex items-center space-x-4">
            <div className="relative flex-shrink-0">
              {writer.avatar ? (
                <Image
                  src={writer.avatar}
                  alt={writer.name}
                  width={60}
                  height={60}
                  className={cn(
                    "rounded-full object-cover transition-all duration-300",
                    imageLoaded ? "opacity-100" : "opacity-0",
                    isHovered && "scale-110"
                  )}
                  onLoad={() => setImageLoaded(true)}
                />
              ) : (
                <div className="w-15 h-15 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg">
                  {getInitials(writer.name)}
                </div>
              )}
              {writer.featured && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                  <Award className="w-3 h-3 text-yellow-800" />
                </div>
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <Link href={`/${locale}/writers/${writer.id}`}>
                <h3 className="font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 truncate">
                  {writer.name}
                </h3>
              </Link>
              <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                {writer.university}
              </p>
              <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                <span className="flex items-center space-x-1">
                  <BookOpen className="w-3 h-3" />
                  <span>{writer.articlesCount || 0}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <Eye className="w-3 h-3" />
                  <span>{writer.totalViews || 0}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </FadeIn>
    );
  }

  if (variant === 'minimal') {
    return (
      <FadeIn>
        <div
          className={cn(
            "glass-card p-6 transition-all duration-300 hover-lift card-hover",
            className
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Header */}
          <div className="text-center mb-4">
            <div className="relative inline-block mb-3">
              {writer.avatar ? (
                <Image
                  src={writer.avatar}
                  alt={writer.name}
                  width={64}
                  height={64}
                  className={cn(
                    "rounded-full object-cover transition-all duration-300 mx-auto",
                    imageLoaded ? "opacity-100" : "opacity-0",
                    isHovered && "scale-110"
                  )}
                  onLoad={() => setImageLoaded(true)}
                />
              ) : (
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg mx-auto">
                  {getInitials(writer.name)}
                </div>
              )}

              {writer.featured && (
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg">
                  <Award className="w-3 h-3 text-yellow-800" />
                </div>
              )}
            </div>

            <Link href={`/${locale}/writers/${writer.id}`}>
              <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-1 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                {writer.name}
              </h3>
            </Link>

            <p className="text-blue-600 dark:text-blue-400 font-medium text-sm mb-2">
              {writer.specialization}
            </p>

            <div className="text-gray-600 dark:text-gray-400 text-sm">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <MapPin className="w-3 h-3" />
                <span>{writer.university}</span>
              </div>
              {writer.location && (
                <div className="text-xs text-gray-500 dark:text-gray-500">
                  {writer.location}
                </div>
              )}
            </div>
          </div>

          {/* Brief Bio */}
          <p className="text-gray-600 dark:text-gray-300 text-sm text-center mb-4 line-clamp-2">
            {writer.bio}
          </p>

          {/* Basic Stats */}
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {writer.articlesCount}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Articles
              </div>
            </div>
            <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {writer.totalViews > 1000 ? `${(writer.totalViews / 1000).toFixed(1)}K` : writer.totalViews}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Views
              </div>
            </div>
          </div>

          {/* Action Button */}
          <Link
            href={`/${locale}/writers/${writer.id}`}
            className="block w-full btn-primary text-center py-2.5 rounded-xl font-semibold text-white hover-lift"
          >
            View Profile
          </Link>
        </div>
      </FadeIn>
    );
  }

  // Default and detailed variants
  const isDetailed = variant === 'detailed';

  return (
    <FadeIn>
      <article 
        className={cn(
          "glass-card overflow-hidden transition-all duration-300 hover-lift card-hover",
          isDetailed ? "p-8" : "p-6",
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Header */}
        <div className="text-center mb-6">
          <div className="relative inline-block mb-4">
            {writer.avatar ? (
              <Image
                src={writer.avatar}
                alt={writer.name}
                width={isDetailed ? 120 : 80}
                height={isDetailed ? 120 : 80}
                className={cn(
                  "rounded-full object-cover transition-all duration-300 mx-auto",
                  imageLoaded ? "opacity-100" : "opacity-0",
                  isHovered && "scale-110"
                )}
                onLoad={() => setImageLoaded(true)}
              />
            ) : (
              <div className={cn(
                "rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold mx-auto",
                isDetailed ? "w-30 h-30 text-3xl" : "w-20 h-20 text-2xl"
              )}>
                {getInitials(writer.name)}
              </div>
            )}
            
            {writer.featured && (
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg">
                <Award className="w-4 h-4 text-yellow-800" />
              </div>
            )}
          </div>

          <Link href={`/${locale}/writers/${writer.id}`}>
            <h2 className={cn(
              "font-bold text-gray-900 dark:text-white mb-2 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200",
              isDetailed ? "text-2xl" : "text-xl"
            )}>
              {writer.name}
            </h2>
          </Link>

          <p className="text-blue-600 dark:text-blue-400 font-medium mb-1">
            {writer.specialization}
          </p>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-center sm:space-x-2 text-gray-600 dark:text-gray-400 text-sm text-center">
            <div className="flex items-center justify-center space-x-1">
              <MapPin className="w-4 h-4" />
              <span>{writer.university}</span>
            </div>
            {writer.location && (
              <div className="flex items-center justify-center space-x-1 mt-1 sm:mt-0">
                <span className="hidden sm:inline">•</span>
                <span>{writer.location}</span>
              </div>
            )}
          </div>
        </div>

        {/* Bio */}
        <p className={cn(
          "text-gray-600 dark:text-gray-300 text-center mb-6",
          isDetailed ? "text-base" : "text-sm line-clamp-3"
        )}>
          {writer.bio}
        </p>

        {/* Academic Info - Only for detailed variant */}
        {isDetailed && (
          <div className="mb-6 space-y-4">
            {/* Academic Details */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-sm">
              {writer.yearOfStudy && (
                <div className="flex items-center space-x-2">
                  <GraduationCap className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-400">{writer.yearOfStudy}</span>
                </div>
              )}
              {writer.gpa && (
                <div className="flex items-center space-x-2">
                  <Trophy className="w-4 h-4 text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-400">GPA: {writer.gpa}</span>
                </div>
              )}
              {writer.lawReview && (
                <div className="flex items-center space-x-2">
                  <Scale className="w-4 h-4 text-purple-600 dark:text-purple-400 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-400">Law Review</span>
                </div>
              )}
              {writer.mootCourt && (
                <div className="flex items-center space-x-2">
                  <Award className="w-4 h-4 text-green-600 dark:text-green-400 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-400">Moot Court</span>
                </div>
              )}
            </div>

            {/* Career Goals */}
            {writer.careerGoals && (
              <div className="text-center">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Career Goals</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 italic">"{writer.careerGoals}"</p>
              </div>
            )}
          </div>
        )}

        {/* Achievements & Experience */}
        {isDetailed && (writer.achievements || writer.internships || writer.awards) && (
          <div className="mb-6">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-3 text-center">
              Professional Experience
            </h3>

            {/* Internships */}
            {writer.internships && writer.internships.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Internships</h4>
                <div className="flex flex-wrap gap-2">
                  {writer.internships.map((internship, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                    >
                      {internship}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Awards */}
            {writer.awards && writer.awards.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Awards & Honors</h4>
                <div className="flex flex-wrap gap-2">
                  {writer.awards.map((award, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 text-xs rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                    >
                      {award}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Publications */}
            {writer.publications && writer.publications.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Publications</h4>
                <div className="flex flex-wrap gap-2">
                  {writer.publications.map((publication, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 text-xs rounded-full bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200"
                    >
                      {publication}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Languages */}
            {writer.languages && writer.languages.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Languages</h4>
                <div className="flex flex-wrap gap-2">
                  {writer.languages.map((language, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 text-xs rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 flex items-center space-x-1"
                    >
                      <Globe className="w-3 h-3" />
                      <span>{language}</span>
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* General Achievements */}
            {writer.achievements && writer.achievements.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Other Achievements</h4>
                <div className="flex flex-wrap gap-2">
                  {writer.achievements.map((achievement, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                    >
                      {achievement}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {writer.articlesCount}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {t('Writers.articles')}
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {writer.totalViews > 1000 ? `${(writer.totalViews / 1000).toFixed(1)}K` : writer.totalViews}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Total Views
            </div>
          </div>
        </div>

        {/* Resume & Portfolio Links */}
        {isDetailed && (writer.resumeUrl || writer.portfolioUrl) && (
          <div className="mb-6">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-3 text-center">
              Professional Documents
            </h3>
            <div className="flex flex-col sm:flex-row gap-3">
              {writer.resumeUrl && (
                <a
                  href={writer.resumeUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-semibold hover-lift transition-all duration-200"
                >
                  <Download className="w-4 h-4" />
                  <span>Download Resume</span>
                </a>
              )}
              {writer.portfolioUrl && (
                <a
                  href={writer.portfolioUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl font-semibold hover-lift transition-all duration-200"
                >
                  <FileText className="w-4 h-4" />
                  <span>View Portfolio</span>
                </a>
              )}
            </div>
          </div>
        )}

        {/* Social Links - Only for detailed variant */}
        {isDetailed && writer.socialLinks && (
          <div className="flex justify-center space-x-3 mb-6">
            {writer.socialLinks.email && (
              <a
                href={`mailto:${writer.socialLinks.email}`}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                title="Email"
              >
                <Mail className="w-4 h-4" />
              </a>
            )}
            {writer.socialLinks.linkedin && (
              <a
                href={writer.socialLinks.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                title="LinkedIn"
              >
                <Linkedin className="w-4 h-4" />
              </a>
            )}
            {writer.socialLinks.twitter && (
              <a
                href={writer.socialLinks.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                title="Twitter"
              >
                <Twitter className="w-4 h-4" />
              </a>
            )}
            {writer.socialLinks.website && (
              <a
                href={writer.socialLinks.website}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                title="Website"
              >
                <ExternalLink className="w-4 h-4" />
              </a>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Link 
            href={`/${locale}/writers/${writer.id}`}
            className="flex-1 btn-primary text-center py-3 rounded-xl font-semibold text-white hover-lift"
          >
            {t('Writers.viewProfile')}
          </Link>
          {isDetailed && (
            <Link 
              href={`/${locale}/writers/${writer.id}/articles`}
              className="flex-1 glass text-center py-3 rounded-xl font-semibold text-gray-900 dark:text-white hover-lift border border-gray-200 dark:border-gray-700"
            >
              View Articles
            </Link>
          )}
        </div>

        {/* Member since */}
        <div className="text-center mt-4 text-xs text-gray-500 dark:text-gray-400">
          Member since {formatDate(writer.joinedDate)}
        </div>
      </article>
    </FadeIn>
  );
}
