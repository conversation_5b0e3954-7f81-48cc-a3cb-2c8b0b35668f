'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { MapPin, BookOpen, Eye, Award, ExternalLink, Mail, Linkedin, Twitter } from 'lucide-react';
import { cn } from '@/lib/utils';
import { FadeIn } from './page-transition';

interface Writer {
  id: string;
  name: string;
  avatar?: string;
  bio: string;
  university: string;
  specialization: string;
  location?: string;
  articlesCount: number;
  totalViews: number;
  joinedDate: string;
  achievements?: string[];
  socialLinks?: {
    email?: string;
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
  featured?: boolean;
}

interface WriterCardProps {
  writer: Writer;
  variant?: 'default' | 'compact' | 'detailed';
  className?: string;
  locale?: string;
}

export function WriterCard({ 
  writer, 
  variant = 'default',
  className,
  locale = 'en'
}: WriterCardProps) {
  const t = useTranslations();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'short'
    }).format(date);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2);
  };

  if (variant === 'compact') {
    return (
      <FadeIn>
        <div 
          className={cn(
            "glass-card p-4 transition-all duration-300 hover-lift card-hover",
            className
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="flex items-center space-x-4">
            <div className="relative flex-shrink-0">
              {writer.avatar ? (
                <Image
                  src={writer.avatar}
                  alt={writer.name}
                  width={60}
                  height={60}
                  className={cn(
                    "rounded-full object-cover transition-all duration-300",
                    imageLoaded ? "opacity-100" : "opacity-0",
                    isHovered && "scale-110"
                  )}
                  onLoad={() => setImageLoaded(true)}
                />
              ) : (
                <div className="w-15 h-15 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg">
                  {getInitials(writer.name)}
                </div>
              )}
              {writer.featured && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                  <Award className="w-3 h-3 text-yellow-800" />
                </div>
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <Link href={`/${locale}/writers/${writer.id}`}>
                <h3 className="font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 truncate">
                  {writer.name}
                </h3>
              </Link>
              <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                {writer.university}
              </p>
              <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                <span className="flex items-center space-x-1">
                  <BookOpen className="w-3 h-3" />
                  <span>{writer.articlesCount}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <Eye className="w-3 h-3" />
                  <span>{writer.totalViews}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </FadeIn>
    );
  }

  // Default and detailed variants
  const isDetailed = variant === 'detailed';

  return (
    <FadeIn>
      <article 
        className={cn(
          "glass-card overflow-hidden transition-all duration-300 hover-lift card-hover",
          isDetailed ? "p-8" : "p-6",
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Header */}
        <div className="text-center mb-6">
          <div className="relative inline-block mb-4">
            {writer.avatar ? (
              <Image
                src={writer.avatar}
                alt={writer.name}
                width={isDetailed ? 120 : 80}
                height={isDetailed ? 120 : 80}
                className={cn(
                  "rounded-full object-cover transition-all duration-300 mx-auto",
                  imageLoaded ? "opacity-100" : "opacity-0",
                  isHovered && "scale-110"
                )}
                onLoad={() => setImageLoaded(true)}
              />
            ) : (
              <div className={cn(
                "rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold mx-auto",
                isDetailed ? "w-30 h-30 text-3xl" : "w-20 h-20 text-2xl"
              )}>
                {getInitials(writer.name)}
              </div>
            )}
            
            {writer.featured && (
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg">
                <Award className="w-4 h-4 text-yellow-800" />
              </div>
            )}
          </div>

          <Link href={`/${locale}/writers/${writer.id}`}>
            <h2 className={cn(
              "font-bold text-gray-900 dark:text-white mb-2 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200",
              isDetailed ? "text-2xl" : "text-xl"
            )}>
              {writer.name}
            </h2>
          </Link>

          <p className="text-blue-600 dark:text-blue-400 font-medium mb-1">
            {writer.specialization}
          </p>
          
          <div className="flex items-center justify-center space-x-2 text-gray-600 dark:text-gray-400 text-sm">
            <MapPin className="w-4 h-4" />
            <span>{writer.university}</span>
            {writer.location && (
              <>
                <span>•</span>
                <span>{writer.location}</span>
              </>
            )}
          </div>
        </div>

        {/* Bio */}
        <p className={cn(
          "text-gray-600 dark:text-gray-300 text-center mb-6",
          isDetailed ? "text-base" : "text-sm line-clamp-3"
        )}>
          {writer.bio}
        </p>

        {/* Achievements */}
        {isDetailed && writer.achievements && writer.achievements.length > 0 && (
          <div className="mb-6">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-3 text-center">
              Achievements
            </h3>
            <div className="flex flex-wrap gap-2 justify-center">
              {writer.achievements.map((achievement, index) => (
                <span 
                  key={index}
                  className="px-3 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                >
                  {achievement}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {writer.articlesCount}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {t('Writers.articles')}
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {writer.totalViews > 1000 ? `${(writer.totalViews / 1000).toFixed(1)}K` : writer.totalViews}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Total Views
            </div>
          </div>
        </div>

        {/* Social Links */}
        {writer.socialLinks && (
          <div className="flex justify-center space-x-3 mb-6">
            {writer.socialLinks.email && (
              <a
                href={`mailto:${writer.socialLinks.email}`}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                title="Email"
              >
                <Mail className="w-4 h-4" />
              </a>
            )}
            {writer.socialLinks.linkedin && (
              <a
                href={writer.socialLinks.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                title="LinkedIn"
              >
                <Linkedin className="w-4 h-4" />
              </a>
            )}
            {writer.socialLinks.twitter && (
              <a
                href={writer.socialLinks.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                title="Twitter"
              >
                <Twitter className="w-4 h-4" />
              </a>
            )}
            {writer.socialLinks.website && (
              <a
                href={writer.socialLinks.website}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                title="Website"
              >
                <ExternalLink className="w-4 h-4" />
              </a>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Link 
            href={`/${locale}/writers/${writer.id}`}
            className="flex-1 btn-primary text-center py-3 rounded-xl font-semibold text-white hover-lift"
          >
            {t('Writers.viewProfile')}
          </Link>
          {isDetailed && (
            <Link 
              href={`/${locale}/writers/${writer.id}/articles`}
              className="flex-1 glass text-center py-3 rounded-xl font-semibold text-gray-900 dark:text-white hover-lift border border-gray-200 dark:border-gray-700"
            >
              View Articles
            </Link>
          )}
        </div>

        {/* Member since */}
        <div className="text-center mt-4 text-xs text-gray-500 dark:text-gray-400">
          Member since {formatDate(writer.joinedDate)}
        </div>
      </article>
    </FadeIn>
  );
}
