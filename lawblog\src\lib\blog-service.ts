import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

const adminSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export interface Writer {
  id: string
  name: string
  email: string
  bio: string | null
  avatarUrl: string | null
  university: string | null
  graduationYear: number | null
  specialization: string | null
  socialLinks: Record<string, any>
  status: 'active' | 'inactive' | 'pending'
  totalPosts: number
  totalViews: number
  joinedDate: string
}

export interface Category {
  id: string
  name: string
  slug: string
  description: string | null
  color: string
  postCount: number
}

export interface Tag {
  id: string
  name: string
  slug: string
  postCount: number
}

export interface Post {
  id: string
  title: string
  slug: string
  excerpt: string | null
  content: string
  featuredImage: string | null
  status: 'draft' | 'published' | 'archived'
  writerId: string
  categoryId: string | null
  readingTime: number
  viewCount: number
  publishedAt: string | null
  createdAt: string
  updatedAt: string
  writer?: Writer
  category?: Category
  tags?: Tag[]
}

// Public functions for blog readers
export async function getPublishedPosts(limit = 10, offset = 0): Promise<Post[]> {
  const { data, error } = await supabase
    .from('posts')
    .select(`
      *,
      writer:writers(*),
      category:categories(*),
      tags:post_tags(tag:tags(*))
    `)
    .eq('status', 'published')
    .order('published_at', { ascending: false })
    .range(offset, offset + limit - 1)

  if (error) {
    console.error('Error fetching posts:', error)
    return []
  }

  return data.map(formatPost)
}

export async function getPostBySlug(slug: string): Promise<Post | null> {
  const { data, error } = await supabase
    .from('posts')
    .select(`
      *,
      writer:writers(*),
      category:categories(*),
      tags:post_tags(tag:tags(*))
    `)
    .eq('slug', slug)
    .eq('status', 'published')
    .single()

  if (error || !data) {
    return null
  }

  // Increment view count
  await supabase.rpc('increment_post_views', { post_id: data.id })

  return formatPost(data)
}

export async function getWriters(): Promise<Writer[]> {
  const { data, error } = await supabase
    .from('writers')
    .select('*')
    .eq('status', 'active')
    .order('total_posts', { ascending: false })

  if (error) {
    console.error('Error fetching writers:', error)
    return []
  }

  return data.map(formatWriter)
}

export async function getWriterBySlug(slug: string): Promise<Writer | null> {
  // For now, we'll use the writer's name as slug (you can add a slug field later)
  const { data, error } = await supabase
    .from('writers')
    .select('*')
    .eq('status', 'active')
    .ilike('name', `%${slug.replace('-', ' ')}%`)
    .single()

  if (error || !data) {
    return null
  }

  return formatWriter(data)
}

export async function getWriterPosts(writerId: string): Promise<Post[]> {
  const { data, error } = await supabase
    .from('posts')
    .select(`
      *,
      writer:writers(*),
      category:categories(*),
      tags:post_tags(tag:tags(*))
    `)
    .eq('writer_id', writerId)
    .eq('status', 'published')
    .order('published_at', { ascending: false })

  if (error) {
    console.error('Error fetching writer posts:', error)
    return []
  }

  return data.map(formatPost)
}

export async function getCategories(): Promise<Category[]> {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('post_count', { ascending: false })

  if (error) {
    console.error('Error fetching categories:', error)
    return []
  }

  return data.map(formatCategory)
}

export async function getCategoryPosts(categorySlug: string): Promise<Post[]> {
  const { data, error } = await supabase
    .from('posts')
    .select(`
      *,
      writer:writers(*),
      category:categories(*),
      tags:post_tags(tag:tags(*))
    `)
    .eq('category.slug', categorySlug)
    .eq('status', 'published')
    .order('published_at', { ascending: false })

  if (error) {
    console.error('Error fetching category posts:', error)
    return []
  }

  return data.map(formatPost)
}

export async function searchPosts(query: string): Promise<Post[]> {
  const { data, error } = await supabase
    .from('posts')
    .select(`
      *,
      writer:writers(*),
      category:categories(*),
      tags:post_tags(tag:tags(*))
    `)
    .eq('status', 'published')
    .or(`title.ilike.%${query}%,content.ilike.%${query}%,excerpt.ilike.%${query}%`)
    .order('published_at', { ascending: false })

  if (error) {
    console.error('Error searching posts:', error)
    return []
  }

  return data.map(formatPost)
}

// Helper functions
function formatPost(data: any): Post {
  return {
    id: data.id,
    title: data.title,
    slug: data.slug,
    excerpt: data.excerpt,
    content: data.content,
    featuredImage: data.featured_image,
    status: data.status,
    writerId: data.writer_id,
    categoryId: data.category_id,
    readingTime: data.reading_time,
    viewCount: data.view_count,
    publishedAt: data.published_at,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
    writer: data.writer ? formatWriter(data.writer) : undefined,
    category: data.category ? formatCategory(data.category) : undefined,
    tags: data.tags ? data.tags.map((t: any) => formatTag(t.tag)) : [],
  }
}

function formatWriter(data: any): Writer {
  return {
    id: data.id,
    name: data.name,
    email: data.email,
    bio: data.bio,
    avatarUrl: data.avatar_url,
    university: data.university,
    graduationYear: data.graduation_year,
    specialization: data.specialization,
    socialLinks: data.social_links || {},
    status: data.status,
    totalPosts: data.total_posts,
    totalViews: data.total_views,
    joinedDate: data.joined_date,
  }
}

function formatCategory(data: any): Category {
  return {
    id: data.id,
    name: data.name,
    slug: data.slug,
    description: data.description,
    color: data.color,
    postCount: data.post_count,
  }
}

function formatTag(data: any): Tag {
  return {
    id: data.id,
    name: data.name,
    slug: data.slug,
    postCount: data.post_count,
  }
}
