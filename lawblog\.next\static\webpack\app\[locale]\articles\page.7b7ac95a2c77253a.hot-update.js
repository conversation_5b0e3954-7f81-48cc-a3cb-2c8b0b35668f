"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/articles/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/articles/page.tsx":
/*!********************************************!*\
  !*** ./src/app/[locale]/articles/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArticlesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _components_blog_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/blog-navigation */ \"(app-pages-browser)/./src/components/blog-navigation.tsx\");\n/* harmony import */ var _components_blog_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/blog-footer */ \"(app-pages-browser)/./src/components/blog-footer.tsx\");\n/* harmony import */ var _components_ui_reading_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/reading-progress */ \"(app-pages-browser)/./src/components/ui/reading-progress.tsx\");\n/* harmony import */ var _components_ui_search__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/search */ \"(app-pages-browser)/./src/components/ui/search.tsx\");\n/* harmony import */ var _components_ui_article_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/article-card */ \"(app-pages-browser)/./src/components/ui/article-card.tsx\");\n/* harmony import */ var _components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/page-transition */ \"(app-pages-browser)/./src/components/ui/page-transition.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Grid,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Grid,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Mock data for demonstration - replace with actual data fetching\nconst mockArticles = [\n    {\n        id: '1',\n        title: 'Constitutional Rights in the Digital Age: Privacy vs Security',\n        excerpt: 'An in-depth analysis of how constitutional rights are evolving in response to digital surveillance and data privacy concerns in modern society.',\n        image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=800&h=400&fit=crop',\n        author: {\n            name: 'Sarah Johnson',\n            avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=100&h=100&fit=crop&crop=face',\n            university: 'Harvard Law School'\n        },\n        category: 'Constitutional Law',\n        publishedAt: '2024-01-15',\n        readingTime: 8,\n        views: 1250,\n        tags: [\n            'privacy',\n            'digital rights',\n            'constitution'\n        ]\n    },\n    {\n        id: '2',\n        title: 'International Trade Law: Navigating Global Commerce',\n        excerpt: 'Understanding the complexities of international trade agreements and their impact on global economic relationships.',\n        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop',\n        author: {\n            name: 'Michael Chen',\n            avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',\n            university: 'Stanford Law School'\n        },\n        category: 'International Law',\n        publishedAt: '2024-01-12',\n        readingTime: 12,\n        views: 890,\n        tags: [\n            'trade',\n            'international',\n            'commerce'\n        ]\n    },\n    {\n        id: '3',\n        title: 'Criminal Justice Reform: A Path Forward',\n        excerpt: 'Examining current criminal justice policies and proposing evidence-based reforms for a more equitable system.',\n        image: 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=800&h=400&fit=crop',\n        author: {\n            name: 'Emily Rodriguez',\n            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',\n            university: 'Yale Law School'\n        },\n        category: 'Criminal Law',\n        publishedAt: '2024-01-10',\n        readingTime: 10,\n        views: 1100,\n        tags: [\n            'reform',\n            'justice',\n            'policy'\n        ]\n    },\n    {\n        id: '4',\n        title: 'Environmental Law and Climate Change',\n        excerpt: 'Exploring the legal frameworks needed to address climate change and environmental protection in the 21st century.',\n        image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?w=800&h=400&fit=crop',\n        author: {\n            name: 'David Kim',\n            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',\n            university: 'Columbia Law School'\n        },\n        category: 'Environmental Law',\n        publishedAt: '2024-01-08',\n        readingTime: 15,\n        views: 750,\n        tags: [\n            'environment',\n            'climate',\n            'sustainability'\n        ]\n    },\n    {\n        id: '5',\n        title: 'Corporate Governance in Modern Business',\n        excerpt: 'Analyzing the evolving landscape of corporate governance and its impact on business ethics and accountability.',\n        image: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=800&h=400&fit=crop',\n        author: {\n            name: 'Lisa Wang',\n            avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=100&h=100&fit=crop&crop=face',\n            university: 'NYU Law School'\n        },\n        category: 'Corporate Law',\n        publishedAt: '2024-01-05',\n        readingTime: 9,\n        views: 920,\n        tags: [\n            'corporate',\n            'governance',\n            'business'\n        ]\n    },\n    {\n        id: '6',\n        title: 'Human Rights in the Digital Era',\n        excerpt: 'Examining how traditional human rights concepts apply to digital spaces and emerging technologies.',\n        image: 'https://images.unsplash.com/photo-*************-d95e436ab8d6?w=800&h=400&fit=crop',\n        author: {\n            name: 'Ahmed Hassan',\n            avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',\n            university: 'Georgetown Law'\n        },\n        category: 'Human Rights',\n        publishedAt: '2024-01-03',\n        readingTime: 11,\n        views: 1050,\n        tags: [\n            'human rights',\n            'digital',\n            'technology'\n        ]\n    }\n];\nfunction ArticlesPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [filteredArticles, setFilteredArticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockArticles);\n    const [isSearchDropdownOpen, setIsSearchDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSearch = (filters)=>{\n        console.log('Search filters:', filters);\n    // Implement filtering logic here\n    // For now, just log the filters\n    };\n    const handleDropdownToggle = (isOpen)=>{\n        setIsSearchDropdownOpen(isOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_reading_progress__WEBPACK_IMPORTED_MODULE_4__.ReadingProgress, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_navigation__WEBPACK_IMPORTED_MODULE_2__.BlogNavigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"gradient-text\",\n                                                    children: t('Articles.title')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                                                children: t('Articles.subtitle')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    delay: 200,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-4xl mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search__WEBPACK_IMPORTED_MODULE_5__.SearchComponent, {\n                                            onSearch: handleSearch,\n                                            placeholder: t('Articles.searchPlaceholder'),\n                                            showFilters: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 space-y-4 sm:space-y-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center sm:space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl sm:text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"All Articles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            filteredArticles.length,\n                                                            \" articles found\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setViewMode('grid'),\n                                                        className: \"p-2 rounded-lg transition-colors duration-200 \".concat(viewMode === 'grid' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'),\n                                                        title: \"Grid View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setViewMode('list'),\n                                                        className: \"p-2 rounded-lg transition-colors duration-200 \".concat(viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'),\n                                                        title: \"List View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6',\n                                        children: [\n                                            ...Array(6)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.ArticleCardSkeleton, {}, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, void 0))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.StaggeredList, {\n                                        delay: 100,\n                                        className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6',\n                                        children: filteredArticles.map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_article_card__WEBPACK_IMPORTED_MODULE_6__.ArticleCard, {\n                                                article: article,\n                                                variant: viewMode === 'list' ? 'compact' : 'default',\n                                                showBookmark: true,\n                                                showShare: true,\n                                                locale: \"en\"\n                                            }, article.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    delay: 600,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"btn-primary px-8 py-3 rounded-xl font-semibold text-white hover-lift\",\n                                            children: t('Articles.loadMore')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_footer__WEBPACK_IMPORTED_MODULE_3__.BlogFooter, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(ArticlesPage, \"Hy6h2/p8MS7jPn+2zHjVY+ll/1s=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations\n    ];\n});\n_c = ArticlesPage;\nvar _c;\n$RefreshReg$(_c, \"ArticlesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vYXJ0aWNsZXMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNDO0FBQ2tCO0FBQ1I7QUFDYTtBQUNWO0FBQ0U7QUFDYTtBQUNUO0FBQ2I7QUFFbEQsa0VBQWtFO0FBQ2xFLE1BQU1hLGVBQWU7SUFDbkI7UUFDRUMsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxRQUFRO1lBQ05DLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxZQUFZO1FBQ2Q7UUFDQUMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxNQUFNO1lBQUM7WUFBVztZQUFrQjtTQUFlO0lBQ3JEO0lBQ0E7UUFDRVosSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxRQUFRO1lBQ05DLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxZQUFZO1FBQ2Q7UUFDQUMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxNQUFNO1lBQUM7WUFBUztZQUFpQjtTQUFXO0lBQzlDO0lBQ0E7UUFDRVosSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxRQUFRO1lBQ05DLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxZQUFZO1FBQ2Q7UUFDQUMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxNQUFNO1lBQUM7WUFBVTtZQUFXO1NBQVM7SUFDdkM7SUFDQTtRQUNFWixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxPQUFPO1FBQ1BDLFFBQVE7WUFDTkMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFlBQVk7UUFDZDtRQUNBQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLE1BQU07WUFBQztZQUFlO1lBQVc7U0FBaUI7SUFDcEQ7SUFDQTtRQUNFWixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxPQUFPO1FBQ1BDLFFBQVE7WUFDTkMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFlBQVk7UUFDZDtRQUNBQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLE1BQU07WUFBQztZQUFhO1lBQWM7U0FBVztJQUMvQztJQUNBO1FBQ0VaLElBQUk7UUFDSkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLE9BQU87UUFDUEMsUUFBUTtZQUNOQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsWUFBWTtRQUNkO1FBQ0FDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsTUFBTTtZQUFDO1lBQWdCO1lBQVc7U0FBYTtJQUNqRDtDQUNEO0FBRWMsU0FBU0M7O0lBQ3RCLE1BQU1DLElBQUkxQiwwREFBZUE7SUFDekIsTUFBTSxDQUFDMkIsVUFBVUMsWUFBWSxHQUFHN0IsK0NBQVFBLENBQWtCO0lBQzFELE1BQU0sQ0FBQzhCLGtCQUFrQkMsb0JBQW9CLEdBQUcvQiwrQ0FBUUEsQ0FBQ1k7SUFDekQsTUFBTSxDQUFDb0Isc0JBQXNCQyx3QkFBd0IsR0FBR2pDLCtDQUFRQSxDQUFDO0lBRWpFLE1BQU1rQyxlQUFlLENBQUNDO1FBQ3BCQyxRQUFRQyxHQUFHLENBQUMsbUJBQW1CRjtJQUMvQixpQ0FBaUM7SUFDakMsZ0NBQWdDO0lBQ2xDO0lBRUEsTUFBTUcsdUJBQXVCLENBQUNDO1FBQzVCTix3QkFBd0JNO0lBQzFCO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDckMsNEVBQWVBOzs7OzswQkFDaEIsOERBQUNGLHVFQUFjQTs7Ozs7MEJBRWYsOERBQUN3QztnQkFBS0QsV0FBVTs7a0NBRWQsOERBQUNFO3dCQUFRRixXQUFVO2tDQUNqQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDbEMsa0VBQU1BOzhDQUNMLDRFQUFDaUM7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRztnREFBR0gsV0FBVTswREFDWiw0RUFBQ0k7b0RBQUtKLFdBQVU7OERBQWlCZCxFQUFFOzs7Ozs7Ozs7OzswREFFckMsOERBQUNtQjtnREFBRUwsV0FBVTswREFDVmQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTVQsOERBQUNwQixrRUFBTUE7b0NBQUN3QyxPQUFPOzhDQUNiLDRFQUFDUDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3BDLGtFQUFlQTs0Q0FDZDJDLFVBQVVkOzRDQUNWZSxhQUFhdEIsRUFBRTs0Q0FDZnVCLGFBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRdkIsOERBQUNQO3dCQUFRRixXQUFVO2tDQUNqQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDbEMsa0VBQU1BOzhDQUNMLDRFQUFDaUM7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNVO3dEQUFHVixXQUFVO2tFQUE4RDs7Ozs7O2tFQUc1RSw4REFBQ0k7d0RBQUtKLFdBQVU7OzREQUNiWCxpQkFBaUJzQixNQUFNOzREQUFDOzs7Ozs7Ozs7Ozs7OzBEQUk3Qiw4REFBQ1o7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDWTt3REFDQ0MsTUFBSzt3REFDTEMsU0FBUyxJQUFNMUIsWUFBWTt3REFDM0JZLFdBQVcsaURBSVYsT0FIQ2IsYUFBYSxTQUNULGtFQUNBO3dEQUVOZCxPQUFNO2tFQUVOLDRFQUFDSixzRkFBSUE7NERBQUMrQixXQUFVOzs7Ozs7Ozs7OztrRUFFbEIsOERBQUNZO3dEQUNDQyxNQUFLO3dEQUNMQyxTQUFTLElBQU0xQixZQUFZO3dEQUMzQlksV0FBVyxpREFJVixPQUhDYixhQUFhLFNBQ1Qsa0VBQ0E7d0RBRU5kLE9BQU07a0VBRU4sNEVBQUNILHNGQUFJQTs0REFBQzhCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBT3hCLDhEQUFDMUMsMkNBQVFBO29DQUFDeUQsd0JBQ1IsOERBQUNoQjt3Q0FBSUMsV0FBV2IsYUFBYSxTQUFTLHlEQUF5RDtrREFDNUY7K0NBQUk2QixNQUFNO3lDQUFHLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxrQkFDckIsOERBQUNuRCx3RUFBbUJBLE1BQU1tRDs7Ozs7Ozs7Ozs4Q0FJOUIsNEVBQUNwRCx5RUFBYUE7d0NBQ1p1QyxPQUFPO3dDQUNQTixXQUNFYixhQUFhLFNBQ1QseURBQ0E7a0RBR0xFLGlCQUFpQjRCLEdBQUcsQ0FBQyxDQUFDRyx3QkFDckIsOERBQUN2RCxvRUFBV0E7Z0RBRVZ1RCxTQUFTQTtnREFDVEMsU0FBU2xDLGFBQWEsU0FBUyxZQUFZO2dEQUMzQ21DLGNBQWM7Z0RBQ2RDLFdBQVc7Z0RBQ1hDLFFBQU87K0NBTEZKLFFBQVFoRCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OENBWXZCLDhEQUFDTixrRUFBTUE7b0NBQUN3QyxPQUFPOzhDQUNiLDRFQUFDUDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ1k7NENBQU9DLE1BQUs7NENBQVNiLFdBQVU7c0RBQzdCZCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUWYsOERBQUN4QiwrREFBVUE7Ozs7Ozs7Ozs7O0FBR2pCO0dBekl3QnVCOztRQUNaekIsc0RBQWVBOzs7S0FESHlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEtyYWltYXRpY1xcRGVza3RvcFxcYWZlcnNhdGFcXGxhd2Jsb2dcXHNyY1xcYXBwXFxbbG9jYWxlXVxcYXJ0aWNsZXNcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgU3VzcGVuc2UsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSAnbmV4dC1pbnRsJztcbmltcG9ydCB7IEJsb2dOYXZpZ2F0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL2Jsb2ctbmF2aWdhdGlvbic7XG5pbXBvcnQgeyBCbG9nRm9vdGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL2Jsb2ctZm9vdGVyJztcbmltcG9ydCB7IFJlYWRpbmdQcm9ncmVzcyB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9yZWFkaW5nLXByb2dyZXNzJztcbmltcG9ydCB7IFNlYXJjaENvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zZWFyY2gnO1xuaW1wb3J0IHsgQXJ0aWNsZUNhcmQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYXJ0aWNsZS1jYXJkJztcbmltcG9ydCB7IEZhZGVJbiwgU3RhZ2dlcmVkTGlzdCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9wYWdlLXRyYW5zaXRpb24nO1xuaW1wb3J0IHsgQXJ0aWNsZUNhcmRTa2VsZXRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9za2VsZXRvbic7XG5pbXBvcnQgeyBGaWx0ZXIsIEdyaWQsIExpc3QgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG4vLyBNb2NrIGRhdGEgZm9yIGRlbW9uc3RyYXRpb24gLSByZXBsYWNlIHdpdGggYWN0dWFsIGRhdGEgZmV0Y2hpbmdcbmNvbnN0IG1vY2tBcnRpY2xlcyA9IFtcbiAge1xuICAgIGlkOiAnMScsXG4gICAgdGl0bGU6ICdDb25zdGl0dXRpb25hbCBSaWdodHMgaW4gdGhlIERpZ2l0YWwgQWdlOiBQcml2YWN5IHZzIFNlY3VyaXR5JyxcbiAgICBleGNlcnB0OiAnQW4gaW4tZGVwdGggYW5hbHlzaXMgb2YgaG93IGNvbnN0aXR1dGlvbmFsIHJpZ2h0cyBhcmUgZXZvbHZpbmcgaW4gcmVzcG9uc2UgdG8gZGlnaXRhbCBzdXJ2ZWlsbGFuY2UgYW5kIGRhdGEgcHJpdmFjeSBjb25jZXJucyBpbiBtb2Rlcm4gc29jaWV0eS4nLFxuICAgIGltYWdlOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1ODk4Mjk1NDU4NTYtZDEwZDU1N2NmOTVmP3c9ODAwJmg9NDAwJmZpdD1jcm9wJyxcbiAgICBhdXRob3I6IHtcbiAgICAgIG5hbWU6ICdTYXJhaCBKb2huc29uJyxcbiAgICAgIGF2YXRhcjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDk0NzkwMTA4NzU1LTI2MTZiNjEyYjc4Nj93PTEwMCZoPTEwMCZmaXQ9Y3JvcCZjcm9wPWZhY2UnLFxuICAgICAgdW5pdmVyc2l0eTogJ0hhcnZhcmQgTGF3IFNjaG9vbCdcbiAgICB9LFxuICAgIGNhdGVnb3J5OiAnQ29uc3RpdHV0aW9uYWwgTGF3JyxcbiAgICBwdWJsaXNoZWRBdDogJzIwMjQtMDEtMTUnLFxuICAgIHJlYWRpbmdUaW1lOiA4LFxuICAgIHZpZXdzOiAxMjUwLFxuICAgIHRhZ3M6IFsncHJpdmFjeScsICdkaWdpdGFsIHJpZ2h0cycsICdjb25zdGl0dXRpb24nXVxuICB9LFxuICB7XG4gICAgaWQ6ICcyJyxcbiAgICB0aXRsZTogJ0ludGVybmF0aW9uYWwgVHJhZGUgTGF3OiBOYXZpZ2F0aW5nIEdsb2JhbCBDb21tZXJjZScsXG4gICAgZXhjZXJwdDogJ1VuZGVyc3RhbmRpbmcgdGhlIGNvbXBsZXhpdGllcyBvZiBpbnRlcm5hdGlvbmFsIHRyYWRlIGFncmVlbWVudHMgYW5kIHRoZWlyIGltcGFjdCBvbiBnbG9iYWwgZWNvbm9taWMgcmVsYXRpb25zaGlwcy4nLFxuICAgIGltYWdlOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MDcwMDMyMTExNjktMGExZGQ3MjI4ZjJkP3c9ODAwJmg9NDAwJmZpdD1jcm9wJyxcbiAgICBhdXRob3I6IHtcbiAgICAgIG5hbWU6ICdNaWNoYWVsIENoZW4nLFxuICAgICAgYXZhdGFyOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE0NzIwOTk2NDU3ODUtNTY1OGFiZjRmZjRlP3c9MTAwJmg9MTAwJmZpdD1jcm9wJmNyb3A9ZmFjZScsXG4gICAgICB1bml2ZXJzaXR5OiAnU3RhbmZvcmQgTGF3IFNjaG9vbCdcbiAgICB9LFxuICAgIGNhdGVnb3J5OiAnSW50ZXJuYXRpb25hbCBMYXcnLFxuICAgIHB1Ymxpc2hlZEF0OiAnMjAyNC0wMS0xMicsXG4gICAgcmVhZGluZ1RpbWU6IDEyLFxuICAgIHZpZXdzOiA4OTAsXG4gICAgdGFnczogWyd0cmFkZScsICdpbnRlcm5hdGlvbmFsJywgJ2NvbW1lcmNlJ11cbiAgfSxcbiAge1xuICAgIGlkOiAnMycsXG4gICAgdGl0bGU6ICdDcmltaW5hbCBKdXN0aWNlIFJlZm9ybTogQSBQYXRoIEZvcndhcmQnLFxuICAgIGV4Y2VycHQ6ICdFeGFtaW5pbmcgY3VycmVudCBjcmltaW5hbCBqdXN0aWNlIHBvbGljaWVzIGFuZCBwcm9wb3NpbmcgZXZpZGVuY2UtYmFzZWQgcmVmb3JtcyBmb3IgYSBtb3JlIGVxdWl0YWJsZSBzeXN0ZW0uJyxcbiAgICBpbWFnZTogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTg5MzkxODg2NjQ1LWQ1MTk0MWJhZjdmYj93PTgwMCZoPTQwMCZmaXQ9Y3JvcCcsXG4gICAgYXV0aG9yOiB7XG4gICAgICBuYW1lOiAnRW1pbHkgUm9kcmlndWV6JyxcbiAgICAgIGF2YXRhcjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDM4NzYxNjgxMDMzLTY0NjFmZmFkOGQ4MD93PTEwMCZoPTEwMCZmaXQ9Y3JvcCZjcm9wPWZhY2UnLFxuICAgICAgdW5pdmVyc2l0eTogJ1lhbGUgTGF3IFNjaG9vbCdcbiAgICB9LFxuICAgIGNhdGVnb3J5OiAnQ3JpbWluYWwgTGF3JyxcbiAgICBwdWJsaXNoZWRBdDogJzIwMjQtMDEtMTAnLFxuICAgIHJlYWRpbmdUaW1lOiAxMCxcbiAgICB2aWV3czogMTEwMCxcbiAgICB0YWdzOiBbJ3JlZm9ybScsICdqdXN0aWNlJywgJ3BvbGljeSddXG4gIH0sXG4gIHtcbiAgICBpZDogJzQnLFxuICAgIHRpdGxlOiAnRW52aXJvbm1lbnRhbCBMYXcgYW5kIENsaW1hdGUgQ2hhbmdlJyxcbiAgICBleGNlcnB0OiAnRXhwbG9yaW5nIHRoZSBsZWdhbCBmcmFtZXdvcmtzIG5lZWRlZCB0byBhZGRyZXNzIGNsaW1hdGUgY2hhbmdlIGFuZCBlbnZpcm9ubWVudGFsIHByb3RlY3Rpb24gaW4gdGhlIDIxc3QgY2VudHVyeS4nLFxuICAgIGltYWdlOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NjkxNjMxMzkzOTQtZGU0ZTRmNDNlNGU1P3c9ODAwJmg9NDAwJmZpdD1jcm9wJyxcbiAgICBhdXRob3I6IHtcbiAgICAgIG5hbWU6ICdEYXZpZCBLaW0nLFxuICAgICAgYXZhdGFyOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MDcwMDMyMTExNjktMGExZGQ3MjI4ZjJkP3c9MTAwJmg9MTAwJmZpdD1jcm9wJmNyb3A9ZmFjZScsXG4gICAgICB1bml2ZXJzaXR5OiAnQ29sdW1iaWEgTGF3IFNjaG9vbCdcbiAgICB9LFxuICAgIGNhdGVnb3J5OiAnRW52aXJvbm1lbnRhbCBMYXcnLFxuICAgIHB1Ymxpc2hlZEF0OiAnMjAyNC0wMS0wOCcsXG4gICAgcmVhZGluZ1RpbWU6IDE1LFxuICAgIHZpZXdzOiA3NTAsXG4gICAgdGFnczogWydlbnZpcm9ubWVudCcsICdjbGltYXRlJywgJ3N1c3RhaW5hYmlsaXR5J11cbiAgfSxcbiAge1xuICAgIGlkOiAnNScsXG4gICAgdGl0bGU6ICdDb3Jwb3JhdGUgR292ZXJuYW5jZSBpbiBNb2Rlcm4gQnVzaW5lc3MnLFxuICAgIGV4Y2VycHQ6ICdBbmFseXppbmcgdGhlIGV2b2x2aW5nIGxhbmRzY2FwZSBvZiBjb3Jwb3JhdGUgZ292ZXJuYW5jZSBhbmQgaXRzIGltcGFjdCBvbiBidXNpbmVzcyBldGhpY3MgYW5kIGFjY291bnRhYmlsaXR5LicsXG4gICAgaW1hZ2U6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU2MDQ3MjM1NC1iMzNmZjBjNDRhNDM/dz04MDAmaD00MDAmZml0PWNyb3AnLFxuICAgIGF1dGhvcjoge1xuICAgICAgbmFtZTogJ0xpc2EgV2FuZycsXG4gICAgICBhdmF0YXI6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ5NDc5MDEwODc1NS0yNjE2YjYxMmI3ODY/dz0xMDAmaD0xMDAmZml0PWNyb3AmY3JvcD1mYWNlJyxcbiAgICAgIHVuaXZlcnNpdHk6ICdOWVUgTGF3IFNjaG9vbCdcbiAgICB9LFxuICAgIGNhdGVnb3J5OiAnQ29ycG9yYXRlIExhdycsXG4gICAgcHVibGlzaGVkQXQ6ICcyMDI0LTAxLTA1JyxcbiAgICByZWFkaW5nVGltZTogOSxcbiAgICB2aWV3czogOTIwLFxuICAgIHRhZ3M6IFsnY29ycG9yYXRlJywgJ2dvdmVybmFuY2UnLCAnYnVzaW5lc3MnXVxuICB9LFxuICB7XG4gICAgaWQ6ICc2JyxcbiAgICB0aXRsZTogJ0h1bWFuIFJpZ2h0cyBpbiB0aGUgRGlnaXRhbCBFcmEnLFxuICAgIGV4Y2VycHQ6ICdFeGFtaW5pbmcgaG93IHRyYWRpdGlvbmFsIGh1bWFuIHJpZ2h0cyBjb25jZXB0cyBhcHBseSB0byBkaWdpdGFsIHNwYWNlcyBhbmQgZW1lcmdpbmcgdGVjaG5vbG9naWVzLicsXG4gICAgaW1hZ2U6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU3MzE2NDcxMzcxNC1kOTVlNDM2YWI4ZDY/dz04MDAmaD00MDAmZml0PWNyb3AnLFxuICAgIGF1dGhvcjoge1xuICAgICAgbmFtZTogJ0FobWVkIEhhc3NhbicsXG4gICAgICBhdmF0YXI6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ3MjA5OTY0NTc4NS01NjU4YWJmNGZmNGU/dz0xMDAmaD0xMDAmZml0PWNyb3AmY3JvcD1mYWNlJyxcbiAgICAgIHVuaXZlcnNpdHk6ICdHZW9yZ2V0b3duIExhdydcbiAgICB9LFxuICAgIGNhdGVnb3J5OiAnSHVtYW4gUmlnaHRzJyxcbiAgICBwdWJsaXNoZWRBdDogJzIwMjQtMDEtMDMnLFxuICAgIHJlYWRpbmdUaW1lOiAxMSxcbiAgICB2aWV3czogMTA1MCxcbiAgICB0YWdzOiBbJ2h1bWFuIHJpZ2h0cycsICdkaWdpdGFsJywgJ3RlY2hub2xvZ3knXVxuICB9XG5dO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcnRpY2xlc1BhZ2UoKSB7XG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoKTtcbiAgY29uc3QgW3ZpZXdNb2RlLCBzZXRWaWV3TW9kZV0gPSB1c2VTdGF0ZTwnZ3JpZCcgfCAnbGlzdCc+KCdncmlkJyk7XG4gIGNvbnN0IFtmaWx0ZXJlZEFydGljbGVzLCBzZXRGaWx0ZXJlZEFydGljbGVzXSA9IHVzZVN0YXRlKG1vY2tBcnRpY2xlcyk7XG4gIGNvbnN0IFtpc1NlYXJjaERyb3Bkb3duT3Blbiwgc2V0SXNTZWFyY2hEcm9wZG93bk9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IGhhbmRsZVNlYXJjaCA9IChmaWx0ZXJzOiBhbnkpID0+IHtcbiAgICBjb25zb2xlLmxvZygnU2VhcmNoIGZpbHRlcnM6JywgZmlsdGVycyk7XG4gICAgLy8gSW1wbGVtZW50IGZpbHRlcmluZyBsb2dpYyBoZXJlXG4gICAgLy8gRm9yIG5vdywganVzdCBsb2cgdGhlIGZpbHRlcnNcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcm9wZG93blRvZ2dsZSA9IChpc09wZW46IGJvb2xlYW4pID0+IHtcbiAgICBzZXRJc1NlYXJjaERyb3Bkb3duT3Blbihpc09wZW4pO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctd2hpdGUgZGFyazpiZy1ncmF5LTkwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDBcIj5cbiAgICAgIDxSZWFkaW5nUHJvZ3Jlc3MgLz5cbiAgICAgIDxCbG9nTmF2aWdhdGlvbiAvPlxuXG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJwdC0xNlwiPlxuICAgICAgICB7LyogSGVhZGVyIFNlY3Rpb24gKi99XG4gICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB2aWEtd2hpdGUgdG8tcHVycGxlLTUwIGRhcms6ZnJvbS1ncmF5LTkwMCBkYXJrOnZpYS1ncmF5LTkwMCBkYXJrOnRvLWdyYXktODAwIHB5LTIwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgICAgPEZhZGVJbj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBzbTp0ZXh0LTR4bCBsZzp0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZ3JhZGllbnQtdGV4dFwiPnt0KCdBcnRpY2xlcy50aXRsZScpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgc206dGV4dC14bCB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMCBtYXgtdy0zeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgICAge3QoJ0FydGljbGVzLnN1YnRpdGxlJyl9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvRmFkZUluPlxuXG4gICAgICAgICAgICB7LyogU2VhcmNoIGFuZCBGaWx0ZXJzICovfVxuICAgICAgICAgICAgPEZhZGVJbiBkZWxheT17MjAwfT5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgIDxTZWFyY2hDb21wb25lbnRcbiAgICAgICAgICAgICAgICAgIG9uU2VhcmNoPXtoYW5kbGVTZWFyY2h9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17dCgnQXJ0aWNsZXMuc2VhcmNoUGxhY2Vob2xkZXInKX1cbiAgICAgICAgICAgICAgICAgIHNob3dGaWx0ZXJzPXt0cnVlfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9GYWRlSW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICB7LyogQXJ0aWNsZXMgU2VjdGlvbiAqL31cbiAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgICB7LyogVmlldyBUb2dnbGUgKi99XG4gICAgICAgICAgICA8RmFkZUluPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIHNtOmp1c3RpZnktYmV0d2VlbiBtYi04IHNwYWNlLXktNCBzbTpzcGFjZS15LTBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIHNtOnNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgc206dGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgIEFsbCBBcnRpY2xlc1xuICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkQXJ0aWNsZXMubGVuZ3RofSBhcnRpY2xlcyBmb3VuZFxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFZpZXdNb2RlKCdncmlkJyl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMiByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgICAgICAgIHZpZXdNb2RlID09PSAnZ3JpZCdcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtMTAwIGRhcms6YmctYmx1ZS05MDAgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgZGFyazpob3Zlcjp0ZXh0LWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJHcmlkIFZpZXdcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8R3JpZCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Vmlld01vZGUoJ2xpc3QnKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0yIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgdmlld01vZGUgPT09ICdsaXN0J1xuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS0xMDAgZGFyazpiZy1ibHVlLTkwMCB0ZXh0LWJsdWUtNjAwIGRhcms6dGV4dC1ibHVlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMCBkYXJrOmhvdmVyOnRleHQtZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkxpc3QgVmlld1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxMaXN0IGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9GYWRlSW4+XG5cbiAgICAgICAgICAgIHsvKiBBcnRpY2xlcyBHcmlkL0xpc3QgKi99XG4gICAgICAgICAgICA8U3VzcGVuc2UgZmFsbGJhY2s9e1xuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17dmlld01vZGUgPT09ICdncmlkJyA/ICdncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC04JyA6ICdzcGFjZS15LTYnfT5cbiAgICAgICAgICAgICAgICB7Wy4uLkFycmF5KDYpXS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgICAgICAgIDxBcnRpY2xlQ2FyZFNrZWxldG9uIGtleT17aX0gLz5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICB9PlxuICAgICAgICAgICAgICA8U3RhZ2dlcmVkTGlzdFxuICAgICAgICAgICAgICAgIGRlbGF5PXsxMDB9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcbiAgICAgICAgICAgICAgICAgIHZpZXdNb2RlID09PSAnZ3JpZCdcbiAgICAgICAgICAgICAgICAgICAgPyAnZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtOCdcbiAgICAgICAgICAgICAgICAgICAgOiAnc3BhY2UteS02J1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtmaWx0ZXJlZEFydGljbGVzLm1hcCgoYXJ0aWNsZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPEFydGljbGVDYXJkXG4gICAgICAgICAgICAgICAgICAgIGtleT17YXJ0aWNsZS5pZH1cbiAgICAgICAgICAgICAgICAgICAgYXJ0aWNsZT17YXJ0aWNsZX1cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17dmlld01vZGUgPT09ICdsaXN0JyA/ICdjb21wYWN0JyA6ICdkZWZhdWx0J31cbiAgICAgICAgICAgICAgICAgICAgc2hvd0Jvb2ttYXJrPXt0cnVlfVxuICAgICAgICAgICAgICAgICAgICBzaG93U2hhcmU9e3RydWV9XG4gICAgICAgICAgICAgICAgICAgIGxvY2FsZT1cImVuXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvU3RhZ2dlcmVkTGlzdD5cbiAgICAgICAgICAgIDwvU3VzcGVuc2U+XG5cbiAgICAgICAgICAgIHsvKiBMb2FkIE1vcmUgKi99XG4gICAgICAgICAgICA8RmFkZUluIGRlbGF5PXs2MDB9PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG10LTEyXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgY2xhc3NOYW1lPVwiYnRuLXByaW1hcnkgcHgtOCBweS0zIHJvdW5kZWQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIGhvdmVyLWxpZnRcIj5cbiAgICAgICAgICAgICAgICAgIHt0KCdBcnRpY2xlcy5sb2FkTW9yZScpfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvRmFkZUluPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L3NlY3Rpb24+XG4gICAgICA8L21haW4+XG5cbiAgICAgIDxCbG9nRm9vdGVyIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU3VzcGVuc2UiLCJ1c2VTdGF0ZSIsInVzZVRyYW5zbGF0aW9ucyIsIkJsb2dOYXZpZ2F0aW9uIiwiQmxvZ0Zvb3RlciIsIlJlYWRpbmdQcm9ncmVzcyIsIlNlYXJjaENvbXBvbmVudCIsIkFydGljbGVDYXJkIiwiRmFkZUluIiwiU3RhZ2dlcmVkTGlzdCIsIkFydGljbGVDYXJkU2tlbGV0b24iLCJHcmlkIiwiTGlzdCIsIm1vY2tBcnRpY2xlcyIsImlkIiwidGl0bGUiLCJleGNlcnB0IiwiaW1hZ2UiLCJhdXRob3IiLCJuYW1lIiwiYXZhdGFyIiwidW5pdmVyc2l0eSIsImNhdGVnb3J5IiwicHVibGlzaGVkQXQiLCJyZWFkaW5nVGltZSIsInZpZXdzIiwidGFncyIsIkFydGljbGVzUGFnZSIsInQiLCJ2aWV3TW9kZSIsInNldFZpZXdNb2RlIiwiZmlsdGVyZWRBcnRpY2xlcyIsInNldEZpbHRlcmVkQXJ0aWNsZXMiLCJpc1NlYXJjaERyb3Bkb3duT3BlbiIsInNldElzU2VhcmNoRHJvcGRvd25PcGVuIiwiaGFuZGxlU2VhcmNoIiwiZmlsdGVycyIsImNvbnNvbGUiLCJsb2ciLCJoYW5kbGVEcm9wZG93blRvZ2dsZSIsImlzT3BlbiIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iLCJzZWN0aW9uIiwiaDEiLCJzcGFuIiwicCIsImRlbGF5Iiwib25TZWFyY2giLCJwbGFjZWhvbGRlciIsInNob3dGaWx0ZXJzIiwiaDIiLCJsZW5ndGgiLCJidXR0b24iLCJ0eXBlIiwib25DbGljayIsImZhbGxiYWNrIiwiQXJyYXkiLCJtYXAiLCJfIiwiaSIsImFydGljbGUiLCJ2YXJpYW50Iiwic2hvd0Jvb2ttYXJrIiwic2hvd1NoYXJlIiwibG9jYWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/articles/page.tsx\n"));

/***/ })

});