'use client';

import { Suspense, useState } from 'react';
import { useTranslations } from 'next-intl';
import { BlogNavigation } from '@/components/blog-navigation';
import { BlogFooter } from '@/components/blog-footer';
import { ReadingProgress } from '@/components/ui/reading-progress';
import { SearchComponent } from '@/components/ui/search';
import { WriterCard } from '@/components/ui/writer-card';
import { FadeIn, StaggeredList } from '@/components/ui/page-transition';
import { WriterCardSkeleton } from '@/components/ui/skeleton';
import { CareerHighlights } from '@/components/ui/career-highlights';
import { Users, Award, BookOpen, TrendingUp, Grid, List, FileText } from 'lucide-react';

// Mock data for demonstration - replace with actual data fetching
const mockWriters = [
  {
    id: '1',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop',
    bio: 'Passionate about constitutional law and digital rights. Currently researching the intersection of technology and privacy law for my law review note.',
    university: 'Harvard Law School',
    specialization: 'Constitutional Law & Technology',
    location: 'Cambridge, MA',
    articlesCount: 12,
    totalViews: 15420,
    joinedDate: '2023-09-15',
    achievements: ['Dean\'s List', 'Law Review Editor', 'Moot Court Champion'],
    socialLinks: {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/sarahjohnson',
      twitter: 'https://twitter.com/sarahjlaw'
    },
    featured: true,
    // Career-focused additions
    yearOfStudy: '3L (Third Year)',
    gpa: '3.85',
    lawReview: true,
    mootCourt: true,
    internships: ['ACLU Summer Intern', 'DOJ Civil Rights Division'],
    publications: ['Harvard Law Review Note on Digital Privacy', 'Constitutional Law Quarterly Article'],
    awards: ['Dean\'s List (6 semesters)', 'Constitutional Law Prize', 'Best Oralist - National Moot Court'],
    barAdmissions: ['Massachusetts (Expected 2024)'],
    languages: ['English (Native)', 'Spanish (Fluent)', 'French (Conversational)'],
    interests: ['Digital Rights', 'Privacy Law', 'First Amendment', 'Technology Policy'],
    careerGoals: 'Seeking a federal clerkship followed by a position at a top-tier firm specializing in constitutional litigation and technology law.',
    resumeUrl: '/resumes/sarah-johnson-resume.pdf',
    portfolioUrl: '/portfolios/sarah-johnson'
  },
  {
    id: '2',
    name: 'Michael Chen',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop',
    bio: 'International trade law enthusiast with a focus on Asia-Pacific economic relations and cross-border commerce regulations. Seeking to bridge legal practice with international business.',
    university: 'Stanford Law School',
    specialization: 'International Trade Law',
    location: 'Palo Alto, CA',
    articlesCount: 8,
    totalViews: 12350,
    joinedDate: '2023-08-20',
    achievements: ['International Law Society President', 'Trade Law Certificate'],
    socialLinks: {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/michaelchen'
    },
    featured: false,
    yearOfStudy: '2L (Second Year)',
    gpa: '3.72',
    lawReview: false,
    mootCourt: true,
    internships: ['WTO Legal Affairs Intern', 'Baker McKenzie Summer Associate'],
    publications: ['Stanford Journal of International Law Comment'],
    awards: ['International Law Society Outstanding Service', 'Asia-Pacific Law Student Award'],
    barAdmissions: ['California (Expected 2025)'],
    languages: ['English (Native)', 'Mandarin (Native)', 'Japanese (Intermediate)'],
    interests: ['International Trade', 'WTO Law', 'Cross-border M&A', 'Asia-Pacific Relations'],
    careerGoals: 'Aiming for a position at an international law firm with strong Asia-Pacific practice, focusing on trade and investment law.',
    resumeUrl: '/resumes/michael-chen-resume.pdf'
  },
  {
    id: '3',
    name: 'Emily Rodriguez',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=200&fit=crop',
    bio: 'Criminal justice reform advocate working on policy research and community outreach programs for underserved populations. Committed to using law as a tool for social justice.',
    university: 'Yale Law School',
    specialization: 'Criminal Law & Policy',
    location: 'New Haven, CT',
    articlesCount: 15,
    totalViews: 18750,
    joinedDate: '2023-07-10',
    achievements: ['Public Interest Fellowship', 'Criminal Justice Clinic', 'Pro Bono Award'],
    socialLinks: {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/emilyrodriguez',
      website: 'https://emilyrodriguez.law'
    },
    featured: true,
    yearOfStudy: '3L (Third Year)',
    gpa: '3.91',
    lawReview: true,
    mootCourt: false,
    internships: ['Public Defender\'s Office', 'ACLU Criminal Law Reform Project', 'Innocence Project'],
    publications: ['Yale Law Journal Note on Sentencing Reform', 'Criminal Justice Policy Brief'],
    awards: ['Public Interest Fellowship', 'Outstanding Pro Bono Service (500+ hours)', 'Criminal Justice Reform Prize'],
    barAdmissions: ['Connecticut (Expected 2024)', 'New York (Expected 2024)'],
    languages: ['English (Native)', 'Spanish (Native)', 'Portuguese (Conversational)'],
    interests: ['Criminal Justice Reform', 'Sentencing Policy', 'Wrongful Convictions', 'Community Justice'],
    careerGoals: 'Dedicated to public interest law, seeking a position with a public defender\'s office or criminal justice reform organization.',
    resumeUrl: '/resumes/emily-rodriguez-resume.pdf',
    portfolioUrl: '/portfolios/emily-rodriguez'
  },
  {
    id: '4',
    name: 'David Kim',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop',
    bio: 'Environmental law researcher focusing on climate change litigation and sustainable development policies.',
    university: 'Columbia Law School',
    specialization: 'Environmental Law',
    location: 'New York, NY',
    articlesCount: 10,
    totalViews: 14200,
    joinedDate: '2023-06-25',
    achievements: ['Environmental Law Review', 'Climate Justice Award'],
    socialLinks: {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/davidkim'
    },
    featured: false
  },
  {
    id: '5',
    name: 'Lisa Wang',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop',
    bio: 'Corporate governance specialist with expertise in securities regulation and business ethics compliance.',
    university: 'NYU Law School',
    specialization: 'Corporate Law',
    location: 'New York, NY',
    articlesCount: 9,
    totalViews: 11800,
    joinedDate: '2023-05-15',
    achievements: ['Business Law Society VP', 'Securities Law Certificate'],
    socialLinks: {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/lisawang'
    },
    featured: false
  },
  {
    id: '6',
    name: 'Ahmed Hassan',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop',
    bio: 'Human rights advocate specializing in international humanitarian law and refugee protection policies.',
    university: 'Georgetown Law',
    specialization: 'Human Rights Law',
    location: 'Washington, DC',
    articlesCount: 13,
    totalViews: 16900,
    joinedDate: '2023-04-30',
    achievements: ['Human Rights Fellowship', 'International Law Clinic', 'Refugee Advocacy Award'],
    socialLinks: {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/ahmedhassan',
      twitter: 'https://twitter.com/ahmedlaw'
    },
    featured: true
  }
];

const stats = [
  { label: 'Law Students', value: '150+', icon: Users },
  { label: 'Top Law Schools', value: '50+', icon: Award },
  { label: 'Published Articles', value: '500+', icon: BookOpen },
  { label: 'Career Opportunities', value: '1M+', icon: TrendingUp }
];

export default function WritersPage() {
  const t = useTranslations();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filteredWriters, setFilteredWriters] = useState(mockWriters);

  const handleSearch = (filters: any) => {
    console.log('Search filters:', filters);
    // Implement filtering logic here
    // For now, just log the filters
  };

  const featuredWriters = filteredWriters.filter(writer => writer.featured);
  const regularWriters = filteredWriters.filter(writer => !writer.featured);

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
      <ReadingProgress />
      <BlogNavigation />

      <main className="pt-16">
        {/* Header Section */}
        <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <FadeIn>
              <div className="text-center mb-12">
                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                  <span className="gradient-text">Future Legal Leaders</span>
                </h1>
                <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto">
                  Discover talented law students from top universities showcasing their expertise through published articles.
                  Connect with future lawyers, explore their academic achievements, and view their professional portfolios.
                </p>
                <div className="mt-6 text-sm text-gray-500 dark:text-gray-400">
                  <span className="inline-flex items-center space-x-2 text-center">
                    <FileText className="w-4 h-4 flex-shrink-0" />
                    <span className="hidden sm:inline">Resume-ready profiles • Portfolio showcases • Career networking</span>
                    <span className="sm:hidden">Professional profiles & portfolios</span>
                  </span>
                </div>
              </div>
            </FadeIn>

            {/* Stats */}
            <FadeIn delay={200}>
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto mb-12">
                {stats.map((stat, index) => (
                  <div key={index} className="glass-card p-4 sm:p-6 text-center hover-lift">
                    <stat.icon className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 sm:mb-3 text-blue-600 dark:text-blue-400" />
                    <div className="text-lg sm:text-2xl font-bold text-gray-900 dark:text-white mb-1">
                      {stat.value}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </FadeIn>

            {/* Search */}
            <FadeIn delay={400}>
              <div className="max-w-2xl mx-auto">
                <SearchComponent
                  onSearch={handleSearch}
                  placeholder="Search writers by name, university, or specialization..."
                  showFilters={true}
                />
              </div>
            </FadeIn>
          </div>
        </section>

        {/* Featured Writers */}
        {featuredWriters.length > 0 && (
          <section className="py-16 bg-gray-50 dark:bg-gray-800/50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <FadeIn>
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    Rising Legal Stars
                  </h2>
                  <p className="text-lg text-gray-600 dark:text-gray-400">
                    Outstanding law students with exceptional academic records and professional achievements
                  </p>
                  <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                    These students have demonstrated excellence in academics, publications, and professional experience
                  </div>
                </div>
              </FadeIn>

              <StaggeredList
                delay={150}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {featuredWriters.map((writer) => (
                  <WriterCard
                    key={writer.id}
                    writer={writer}
                    variant="detailed"
                    locale="en"
                  />
                ))}
              </StaggeredList>
            </div>
          </section>
        )}

        {/* Career Highlights for Recruiters */}
        <CareerHighlights />

        {/* All Writers */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* View Toggle */}
            <FadeIn>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 space-y-4 sm:space-y-0">
                <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4">
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                    All Writers
                  </h2>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {filteredWriters.length} writers found
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg transition-colors duration-200 ${
                      viewMode === 'grid'
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                        : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                    }`}
                    title="Grid View"
                  >
                    <Grid className="w-5 h-5" />
                  </button>
                  <button
                    type="button"
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg transition-colors duration-200 ${
                      viewMode === 'list'
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                        : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                    }`}
                    title="List View"
                  >
                    <List className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </FadeIn>

            {/* Writers Grid/List */}
            <Suspense fallback={
              <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-4'}>
                {[...Array(6)].map((_, i) => (
                  <WriterCardSkeleton key={i} />
                ))}
              </div>
            }>
              <StaggeredList
                delay={100}
                className={
                  viewMode === 'grid'
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
                    : 'space-y-4'
                }
              >
                {regularWriters.map((writer) => (
                  <WriterCard
                    key={writer.id}
                    writer={writer}
                    variant={viewMode === 'list' ? 'compact' : 'minimal'}
                    locale="en"
                  />
                ))}
              </StaggeredList>
            </Suspense>
          </div>
        </section>
      </main>

      <BlogFooter />
    </div>
  );
}
