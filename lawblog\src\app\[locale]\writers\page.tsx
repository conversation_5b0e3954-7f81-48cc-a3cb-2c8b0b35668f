import { BlogNavigation } from '@/components/blog-navigation'
import { WritersGrid } from '@/components/writers-grid'
import { BlogFooter } from '@/components/blog-footer'
import { getWriters } from '@/lib/blog-service'

export default async function WritersPage() {
  const writers = await getWriters()

  return (
    <div className="min-h-screen bg-gray-50">
      <BlogNavigation />
      <main className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              Meet Our Writers
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the talented law students behind our insightful articles. Learn about their backgrounds, specializations, and academic journeys.
            </p>
          </div>

          <WritersGrid writers={writers} />
        </div>
      </main>
      <BlogFooter />
    </div>
  )
}
