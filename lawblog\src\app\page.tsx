import { SimpleNavigation } from '@/components/simple-navigation'
import { SimpleFooter } from '@/components/simple-footer'
import { getPublishedPosts, getWriters } from '@/lib/blog-service'
import { FeaturedArticlesSimple } from '@/components/featured-articles-simple'
import { FeaturedWritersSimple } from '@/components/featured-writers-simple'

export default async function Home() {
  try {
    const [posts, writers] = await Promise.all([
      getPublishedPosts(6),
      getWriters()
    ])

    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
        <SimpleNavigation />
        <main>
          {/* Hero Section */}
          <section className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 flex items-center justify-center pt-16 relative overflow-hidden">
            {/* Background Elements */}
            <div className="absolute inset-0">
              <div className="absolute top-20 left-4 sm:left-10 w-12 h-12 sm:w-20 sm:h-20 bg-blue-500/20 dark:bg-blue-400/10 rounded-full blur-xl animate-pulse"></div>
              <div className="absolute top-40 right-4 sm:right-20 w-20 h-20 sm:w-32 sm:h-32 bg-purple-500/20 dark:bg-purple-400/10 rounded-full blur-xl animate-pulse delay-1000"></div>
              <div className="absolute bottom-20 left-1/4 w-16 h-16 sm:w-24 sm:h-24 bg-indigo-500/20 dark:bg-indigo-400/10 rounded-full blur-xl animate-pulse delay-500"></div>
            </div>

            <div className="relative z-10 text-center px-4 max-w-6xl mx-auto">
              {/* Welcome Badge */}
              <div className="inline-flex items-center space-x-2 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm px-3 py-2 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium text-blue-600 dark:text-blue-400 mb-6 sm:mb-8 border border-blue-200 dark:border-blue-800">
                <span>✨</span>
                <span>Welcome to the Future of Legal Education</span>
              </div>

              {/* Main Heading */}
              <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold text-gray-900 dark:text-white mb-6 sm:mb-8 leading-tight">
                <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent font-ethiopic">
                  አፈርሳታ
                </span>
                <br />
                <span className="text-2xl sm:text-4xl lg:text-5xl text-gray-700 dark:text-gray-300">
                  Where Legal Minds Share Stories
                </span>
              </h1>

              {/* Subtitle */}
              <p className="text-lg sm:text-xl lg:text-2xl text-gray-600 dark:text-gray-400 mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed">
                Discover insightful legal articles written by talented law students from top universities around the world. Read, learn, and explore the future of legal practice.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-16 sm:mb-20">
                <a
                  href="/articles"
                  className="group inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 sm:py-4 sm:px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl text-sm sm:text-base w-full sm:w-auto justify-center"
                >
                  <span>Explore Articles</span>
                  <span className="group-hover:translate-x-1 transition-transform">→</span>
                </a>
                
                <a
                  href="/writers"
                  className="inline-flex items-center space-x-2 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm font-semibold py-3 px-6 sm:py-4 sm:px-8 rounded-xl transition-all duration-300 hover:shadow-lg text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 text-sm sm:text-base w-full sm:w-auto justify-center"
                >
                  <span>👥</span>
                  <span>Meet Writers</span>
                </a>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-8">
                <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm p-4 sm:p-6 rounded-2xl text-center border border-white/20 dark:border-gray-700/20 hover:shadow-xl transition-all duration-300">
                  <div className="inline-flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl mb-3 sm:mb-4">
                    <span className="text-white text-lg">📚</span>
                  </div>
                  <div className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-1 sm:mb-2">{posts.length}+</div>
                  <div className="text-gray-600 dark:text-gray-400 font-medium text-sm sm:text-base">Articles Published</div>
                </div>
                
                <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm p-4 sm:p-6 rounded-2xl text-center border border-white/20 dark:border-gray-700/20 hover:shadow-xl transition-all duration-300">
                  <div className="inline-flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl mb-3 sm:mb-4">
                    <span className="text-white text-lg">👩‍🎓</span>
                  </div>
                  <div className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-1 sm:mb-2">{writers.length}+</div>
                  <div className="text-gray-600 dark:text-gray-400 font-medium text-sm sm:text-base">Talented Writers</div>
                </div>
                
                <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm p-4 sm:p-6 rounded-2xl text-center border border-white/20 dark:border-gray-700/20 hover:shadow-xl transition-all duration-300">
                  <div className="inline-flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl mb-3 sm:mb-4">
                    <span className="text-white text-lg">📈</span>
                  </div>
                  <div className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-1 sm:mb-2">1K+</div>
                  <div className="text-gray-600 dark:text-gray-400 font-medium text-sm sm:text-base">Monthly Readers</div>
                </div>
              </div>
            </div>

            {/* Scroll Indicator */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
              <div className="w-6 h-10 border-2 border-gray-400 dark:border-gray-600 rounded-full flex justify-center">
                <div className="w-1 h-3 bg-gray-400 dark:bg-gray-600 rounded-full mt-2 animate-pulse"></div>
              </div>
            </div>
          </section>

          {/* Featured Articles */}
          {posts.length > 0 && <FeaturedArticlesSimple posts={posts.slice(0, 3)} />}

          {/* Featured Writers */}
          {writers.length > 0 && <FeaturedWritersSimple writers={writers.slice(0, 4)} />}
        </main>
        <SimpleFooter />
      </div>
    )
  } catch (error) {
    console.error('Error loading homepage:', error)
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
        <SimpleNavigation />
        <main className="pt-16 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">አፈርሳታ</h1>
            <p className="text-xl text-gray-600 dark:text-gray-400">Legal Blog Platform</p>
          </div>
        </main>
        <SimpleFooter />
      </div>
    )
  }
}
