"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/articles/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/articles/page.tsx":
/*!********************************************!*\
  !*** ./src/app/[locale]/articles/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArticlesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _components_blog_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/blog-navigation */ \"(app-pages-browser)/./src/components/blog-navigation.tsx\");\n/* harmony import */ var _components_blog_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/blog-footer */ \"(app-pages-browser)/./src/components/blog-footer.tsx\");\n/* harmony import */ var _components_ui_reading_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/reading-progress */ \"(app-pages-browser)/./src/components/ui/reading-progress.tsx\");\n/* harmony import */ var _components_ui_search__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/search */ \"(app-pages-browser)/./src/components/ui/search.tsx\");\n/* harmony import */ var _components_ui_article_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/article-card */ \"(app-pages-browser)/./src/components/ui/article-card.tsx\");\n/* harmony import */ var _components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/page-transition */ \"(app-pages-browser)/./src/components/ui/page-transition.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Grid,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Grid,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Mock data for demonstration - replace with actual data fetching\nconst mockArticles = [\n    {\n        id: '1',\n        title: 'Constitutional Rights in the Digital Age: Privacy vs Security',\n        excerpt: 'An in-depth analysis of how constitutional rights are evolving in response to digital surveillance and data privacy concerns in modern society.',\n        image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=800&h=400&fit=crop',\n        author: {\n            name: 'Sarah Johnson',\n            avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=100&h=100&fit=crop&crop=face',\n            university: 'Harvard Law School'\n        },\n        category: 'Constitutional Law',\n        publishedAt: '2024-01-15',\n        readingTime: 8,\n        views: 1250,\n        tags: [\n            'privacy',\n            'digital rights',\n            'constitution'\n        ]\n    },\n    {\n        id: '2',\n        title: 'International Trade Law: Navigating Global Commerce',\n        excerpt: 'Understanding the complexities of international trade agreements and their impact on global economic relationships.',\n        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop',\n        author: {\n            name: 'Michael Chen',\n            avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',\n            university: 'Stanford Law School'\n        },\n        category: 'International Law',\n        publishedAt: '2024-01-12',\n        readingTime: 12,\n        views: 890,\n        tags: [\n            'trade',\n            'international',\n            'commerce'\n        ]\n    },\n    {\n        id: '3',\n        title: 'Criminal Justice Reform: A Path Forward',\n        excerpt: 'Examining current criminal justice policies and proposing evidence-based reforms for a more equitable system.',\n        image: 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=800&h=400&fit=crop',\n        author: {\n            name: 'Emily Rodriguez',\n            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',\n            university: 'Yale Law School'\n        },\n        category: 'Criminal Law',\n        publishedAt: '2024-01-10',\n        readingTime: 10,\n        views: 1100,\n        tags: [\n            'reform',\n            'justice',\n            'policy'\n        ]\n    },\n    {\n        id: '4',\n        title: 'Environmental Law and Climate Change',\n        excerpt: 'Exploring the legal frameworks needed to address climate change and environmental protection in the 21st century.',\n        image: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?w=800&h=400&fit=crop',\n        author: {\n            name: 'David Kim',\n            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',\n            university: 'Columbia Law School'\n        },\n        category: 'Environmental Law',\n        publishedAt: '2024-01-08',\n        readingTime: 15,\n        views: 750,\n        tags: [\n            'environment',\n            'climate',\n            'sustainability'\n        ]\n    },\n    {\n        id: '5',\n        title: 'Corporate Governance in Modern Business',\n        excerpt: 'Analyzing the evolving landscape of corporate governance and its impact on business ethics and accountability.',\n        image: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=800&h=400&fit=crop',\n        author: {\n            name: 'Lisa Wang',\n            avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=100&h=100&fit=crop&crop=face',\n            university: 'NYU Law School'\n        },\n        category: 'Corporate Law',\n        publishedAt: '2024-01-05',\n        readingTime: 9,\n        views: 920,\n        tags: [\n            'corporate',\n            'governance',\n            'business'\n        ]\n    },\n    {\n        id: '6',\n        title: 'Human Rights in the Digital Era',\n        excerpt: 'Examining how traditional human rights concepts apply to digital spaces and emerging technologies.',\n        image: 'https://images.unsplash.com/photo-*************-d95e436ab8d6?w=800&h=400&fit=crop',\n        author: {\n            name: 'Ahmed Hassan',\n            avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',\n            university: 'Georgetown Law'\n        },\n        category: 'Human Rights',\n        publishedAt: '2024-01-03',\n        readingTime: 11,\n        views: 1050,\n        tags: [\n            'human rights',\n            'digital',\n            'technology'\n        ]\n    }\n];\nfunction ArticlesPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [filteredArticles, setFilteredArticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockArticles);\n    const [isSearchDropdownOpen, setIsSearchDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSearch = (filters)=>{\n        console.log('Search filters:', filters);\n    // Implement filtering logic here\n    // For now, just log the filters\n    };\n    const handleDropdownToggle = (isOpen)=>{\n        setIsSearchDropdownOpen(isOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_reading_progress__WEBPACK_IMPORTED_MODULE_4__.ReadingProgress, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isSearchDropdownOpen ? 'blur-sm' : '', \" transition-all duration-300\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_navigation__WEBPACK_IMPORTED_MODULE_2__.BlogNavigation, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12 \".concat(isSearchDropdownOpen ? 'blur-sm' : '', \" transition-all duration-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"gradient-text\",\n                                                    children: t('Articles.title')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                                                children: t('Articles.subtitle')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    delay: 200,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-4xl mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search__WEBPACK_IMPORTED_MODULE_5__.SearchComponent, {\n                                            onSearch: handleSearch,\n                                            placeholder: t('Articles.searchPlaceholder'),\n                                            showFilters: true,\n                                            onDropdownToggle: handleDropdownToggle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 \".concat(isSearchDropdownOpen ? 'blur-sm' : '', \" transition-all duration-300\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 space-y-4 sm:space-y-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center sm:space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl sm:text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"All Articles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            filteredArticles.length,\n                                                            \" articles found\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setViewMode('grid'),\n                                                        className: \"p-2 rounded-lg transition-colors duration-200 \".concat(viewMode === 'grid' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'),\n                                                        title: \"Grid View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setViewMode('list'),\n                                                        className: \"p-2 rounded-lg transition-colors duration-200 \".concat(viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'),\n                                                        title: \"List View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_List_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6',\n                                        children: [\n                                            ...Array(6)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.ArticleCardSkeleton, {}, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, void 0))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.StaggeredList, {\n                                        delay: 100,\n                                        className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6',\n                                        children: filteredArticles.map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_article_card__WEBPACK_IMPORTED_MODULE_6__.ArticleCard, {\n                                                article: article,\n                                                variant: viewMode === 'list' ? 'compact' : 'default',\n                                                showBookmark: true,\n                                                showShare: true,\n                                                locale: \"en\"\n                                            }, article.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    delay: 600,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"btn-primary px-8 py-3 rounded-xl font-semibold text-white hover-lift\",\n                                            children: t('Articles.loadMore')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isSearchDropdownOpen ? 'blur-sm' : '', \" transition-all duration-300\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_footer__WEBPACK_IMPORTED_MODULE_3__.BlogFooter, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(ArticlesPage, \"Hy6h2/p8MS7jPn+2zHjVY+ll/1s=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations\n    ];\n});\n_c = ArticlesPage;\nvar _c;\n$RefreshReg$(_c, \"ArticlesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/articles/page.tsx\n"));

/***/ })

});