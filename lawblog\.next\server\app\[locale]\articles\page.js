/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/articles/page";
exports.ids = ["app/[locale]/articles/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./am.json": [
		"(rsc)/./messages/am.json",
		"_rsc_messages_am_json"
	],
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Farticles%2Fpage&page=%2F%5Blocale%5D%2Farticles%2Fpage&appPaths=%2F%5Blocale%5D%2Farticles%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Farticles%2Fpage.tsx&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5Cafersata%5Clawblog%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5Cafersata%5Clawblog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Farticles%2Fpage&page=%2F%5Blocale%5D%2Farticles%2Fpage&appPaths=%2F%5Blocale%5D%2Farticles%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Farticles%2Fpage.tsx&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5Cafersata%5Clawblog%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5Cafersata%5Clawblog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/articles/page.tsx */ \"(rsc)/./src/app/[locale]/articles/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'articles',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/articles/page\",\n        pathname: \"/[locale]/articles\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkYlNUJsb2NhbGUlNUQlMkZhcnRpY2xlcyUyRnBhZ2UmcGFnZT0lMkYlNUJsb2NhbGUlNUQlMkZhcnRpY2xlcyUyRnBhZ2UmYXBwUGF0aHM9JTJGJTVCbG9jYWxlJTVEJTJGYXJ0aWNsZXMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGJTVCbG9jYWxlJTVEJTJGYXJ0aWNsZXMlMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDS3JhaW1hdGljJTVDRGVza3RvcCU1Q2FmZXJzYXRhJTVDbGF3YmxvZyU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDS3JhaW1hdGljJTVDRGVza3RvcCU1Q2FmZXJzYXRhJTVDbGF3YmxvZyZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0JBQXNCLG9KQUEyRztBQUNqSSxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLGdPQUFtRjtBQUN6RyxzQkFBc0Isc0tBQXFIO0FBQzNJLG9CQUFvQixvTEFBNkg7QUFHL0k7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBbVE7QUFDdlM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHNmQUFtUTtBQUN2UztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUdyQjtBQUNGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUdFO0FBQ0Y7QUFDTyx3QkFBd0IsdUdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vZHVsZTAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEtyYWltYXRpY1xcXFxEZXNrdG9wXFxcXGFmZXJzYXRhXFxcXGxhd2Jsb2dcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTIgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlNCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcS3JhaW1hdGljXFxcXERlc2t0b3BcXFxcYWZlcnNhdGFcXFxcbGF3YmxvZ1xcXFxzcmNcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXGxheW91dC50c3hcIik7XG5jb25zdCBwYWdlNSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcS3JhaW1hdGljXFxcXERlc2t0b3BcXFxcYWZlcnNhdGFcXFxcbGF3YmxvZ1xcXFxzcmNcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXGFydGljbGVzXFxcXHBhZ2UudHN4XCIpO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zc3InXG59O1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdbbG9jYWxlXScsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2FydGljbGVzJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U1LCBcIkM6XFxcXFVzZXJzXFxcXEtyYWltYXRpY1xcXFxEZXNrdG9wXFxcXGFmZXJzYXRhXFxcXGxhd2Jsb2dcXFxcc3JjXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFxhcnRpY2xlc1xcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTQsIFwiQzpcXFxcVXNlcnNcXFxcS3JhaW1hdGljXFxcXERlc2t0b3BcXFxcYWZlcnNhdGFcXFxcbGF3YmxvZ1xcXFxzcmNcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXGxheW91dC50c3hcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFDOlxcXFxVc2Vyc1xcXFxLcmFpbWF0aWNcXFxcRGVza3RvcFxcXFxhZmVyc2F0YVxcXFxsYXdibG9nXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlMCwgXCJDOlxcXFxVc2Vyc1xcXFxLcmFpbWF0aWNcXFxcRGVza3RvcFxcXFxhZmVyc2F0YVxcXFxsYXdibG9nXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUM6XFxcXFVzZXJzXFxcXEtyYWltYXRpY1xcXFxEZXNrdG9wXFxcXGFmZXJzYXRhXFxcXGxhd2Jsb2dcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkM6XFxcXFVzZXJzXFxcXEtyYWltYXRpY1xcXFxEZXNrdG9wXFxcXGFmZXJzYXRhXFxcXGxhd2Jsb2dcXFxcc3JjXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFxhcnRpY2xlc1xcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvW2xvY2FsZV0vYXJ0aWNsZXMvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvW2xvY2FsZV0vYXJ0aWNsZXNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Farticles%2Fpage&page=%2F%5Blocale%5D%2Farticles%2Fpage&appPaths=%2F%5Blocale%5D%2Farticles%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Farticles%2Fpage.tsx&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5Cafersata%5Clawblog%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5Cafersata%5Clawblog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Ethiopic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22ethiopic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ethiopic%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansEthiopic%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Ethiopic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22ethiopic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ethiopic%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansEthiopic%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Ethiopic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22ethiopic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ethiopic%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansEthiopic%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Ethiopic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22ethiopic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ethiopic%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansEthiopic%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(rsc)/./src/components/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(rsc)/./src/components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Ethiopic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22ethiopic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ethiopic%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansEthiopic%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Carticles-list.tsx%22%2C%22ids%22%3A%5B%22ArticlesList%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-footer.tsx%22%2C%22ids%22%3A%5B%22BlogFooter%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-navigation.tsx%22%2C%22ids%22%3A%5B%22BlogNavigation%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Carticles-list.tsx%22%2C%22ids%22%3A%5B%22ArticlesList%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-footer.tsx%22%2C%22ids%22%3A%5B%22BlogFooter%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-navigation.tsx%22%2C%22ids%22%3A%5B%22BlogNavigation%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/articles-list.tsx */ \"(rsc)/./src/components/articles-list.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/blog-footer.tsx */ \"(rsc)/./src/components/blog-footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/blog-navigation.tsx */ \"(rsc)/./src/components/blog-navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0tyYWltYXRpYyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZmVyc2F0YSU1QyU1Q2xhd2Jsb2clNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYXJ0aWNsZXMtbGlzdC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBcnRpY2xlc0xpc3QlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDS3JhaW1hdGljJTVDJTVDRGVza3RvcCU1QyU1Q2FmZXJzYXRhJTVDJTVDbGF3YmxvZyU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNibG9nLWZvb3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJCbG9nRm9vdGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0tyYWltYXRpYyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZmVyc2F0YSU1QyU1Q2xhd2Jsb2clNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYmxvZy1uYXZpZ2F0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkJsb2dOYXZpZ2F0aW9uJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBMko7QUFDM0o7QUFDQSw0S0FBdUo7QUFDdko7QUFDQSxvTEFBK0oiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkFydGljbGVzTGlzdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEtyYWltYXRpY1xcXFxEZXNrdG9wXFxcXGFmZXJzYXRhXFxcXGxhd2Jsb2dcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcYXJ0aWNsZXMtbGlzdC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkJsb2dGb290ZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxLcmFpbWF0aWNcXFxcRGVza3RvcFxcXFxhZmVyc2F0YVxcXFxsYXdibG9nXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGJsb2ctZm9vdGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQmxvZ05hdmlnYXRpb25cIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxLcmFpbWF0aWNcXFxcRGVza3RvcFxcXFxhZmVyc2F0YVxcXFxsYXdibG9nXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGJsb2ctbmF2aWdhdGlvbi50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Carticles-list.tsx%22%2C%22ids%22%3A%5B%22ArticlesList%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-footer.tsx%22%2C%22ids%22%3A%5B%22BlogFooter%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-navigation.tsx%22%2C%22ids%22%3A%5B%22BlogNavigation%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcS3JhaW1hdGljXFxEZXNrdG9wXFxhZmVyc2F0YVxcbGF3YmxvZ1xcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/articles/page.tsx":
/*!********************************************!*\
  !*** ./src/app/[locale]/articles/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArticlesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_blog_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/blog-navigation */ \"(rsc)/./src/components/blog-navigation.tsx\");\n/* harmony import */ var _components_articles_list__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/articles-list */ \"(rsc)/./src/components/articles-list.tsx\");\n/* harmony import */ var _components_blog_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/blog-footer */ \"(rsc)/./src/components/blog-footer.tsx\");\n/* harmony import */ var _lib_blog_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/blog-service */ \"(rsc)/./src/lib/blog-service.ts\");\n\n\n\n\n\nasync function ArticlesPage() {\n    const posts = await (0,_lib_blog_service__WEBPACK_IMPORTED_MODULE_4__.getPublishedPosts)(20);\n    const categories = await (0,_lib_blog_service__WEBPACK_IMPORTED_MODULE_4__.getCategories)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_navigation__WEBPACK_IMPORTED_MODULE_1__.BlogNavigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\",\n                                    children: \"Legal Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Explore insightful articles written by talented law students from top universities around the world.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_articles_list__WEBPACK_IMPORTED_MODULE_2__.ArticlesList, {\n                            posts: posts,\n                            categories: categories\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_footer__WEBPACK_IMPORTED_MODULE_3__.BlogFooter, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\articles\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/articles/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\[locale]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\",\"display\":\"swap\"}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\[locale]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_Ethiopic_arguments_subsets_ethiopic_variable_font_ethiopic_display_swap_variableName_notoSansEthiopic___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Noto_Sans_Ethiopic\",\"arguments\":[{\"subsets\":[\"ethiopic\"],\"variable\":\"--font-ethiopic\",\"display\":\"swap\"}],\"variableName\":\"notoSansEthiopic\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\[locale]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_Ethiopic\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"ethiopic\\\"],\\\"variable\\\":\\\"--font-ethiopic\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansEthiopic\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_Ethiopic_arguments_subsets_ethiopic_variable_font_ethiopic_display_swap_variableName_notoSansEthiopic___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_Ethiopic_arguments_subsets_ethiopic_variable_font_ethiopic_display_swap_variableName_notoSansEthiopic___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./src/components/theme-provider.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"አፈርሳታ - Legal Blog Platform\",\n    description: \"Where legal minds share stories. Discover insightful articles written by talented law students from top universities around the world.\",\n    keywords: [\n        \"legal blog\",\n        \"law students\",\n        \"legal education\",\n        \"legal articles\",\n        \"law school\",\n        \"አፈርሳታ\"\n    ],\n    authors: [\n        {\n            name: \"አፈርሳታ Team\"\n        }\n    ],\n    creator: \"አፈርሳታ\",\n    publisher: \"አፈርሳታ\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"http://localhost:3000\" || 0,\n        title: \"አፈርሳታ - Legal Blog Platform\",\n        description: \"Where legal minds share stories. Discover insightful articles written by talented law students.\",\n        siteName: \"አፈርሳታ\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"አፈርሳታ - Legal Blog Platform\",\n        description: \"Where legal minds share stories. Discover insightful articles written by talented law students.\",\n        creator: \"@afersata\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nasync function LocaleLayout({ children, params }) {\n    const { locale } = await params;\n    // Validate locale\n    const locales = [\n        'en',\n        'am'\n    ];\n    if (!locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.notFound)();\n    }\n    // Get messages for the locale\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        className: `${(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().variable)} ${(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_8___default().variable)} ${(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_Ethiopic_arguments_subsets_ethiopic_variable_font_ethiopic_display_swap_variableName_notoSansEthiopic___WEBPACK_IMPORTED_MODULE_9___default().variable)} dark`,\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-white dark:bg-gray-900 font-inter antialiased transition-colors duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                messages: messages,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                                position: \"top-right\",\n                                toastOptions: {\n                                    duration: 4000,\n                                    className: 'dark:bg-gray-800 dark:text-white bg-white text-gray-900',\n                                    style: {\n                                        borderRadius: \"12px\",\n                                        padding: \"16px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\"\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1512b03e197c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEtyYWltYXRpY1xcRGVza3RvcFxcYWZlcnNhdGFcXGxhd2Jsb2dcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE1MTJiMDNlMTk3Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\",\"display\":\"swap\"}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_Ethiopic_arguments_subsets_ethiopic_variable_font_ethiopic_display_swap_variableName_notoSansEthiopic___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Sans_Ethiopic\",\"arguments\":[{\"subsets\":[\"ethiopic\"],\"variable\":\"--font-ethiopic\",\"display\":\"swap\"}],\"variableName\":\"notoSansEthiopic\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_Ethiopic\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"ethiopic\\\"],\\\"variable\\\":\\\"--font-ethiopic\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansEthiopic\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_Ethiopic_arguments_subsets_ethiopic_variable_font_ethiopic_display_swap_variableName_notoSansEthiopic___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_Ethiopic_arguments_subsets_ethiopic_variable_font_ethiopic_display_swap_variableName_notoSansEthiopic___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"አፈርሳታ - Legal Blog Platform\",\n    description: \"Where legal minds share stories. Discover insightful articles written by talented law students from top universities around the world.\",\n    keywords: [\n        \"legal blog\",\n        \"law students\",\n        \"legal education\",\n        \"legal articles\",\n        \"law school\"\n    ],\n    authors: [\n        {\n            name: \"አፈርሳታ Team\"\n        }\n    ],\n    creator: \"አፈርሳታ\",\n    publisher: \"አፈርሳታ\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    openGraph: {\n        title: \"አፈርሳታ - Legal Blog Platform\",\n        description: \"Where legal minds share stories. Discover insightful articles written by talented law students.\",\n        url: \"/\",\n        siteName: \"አፈርሳታ\",\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"አፈርሳታ - Legal Blog Platform\",\n        description: \"Where legal minds share stories. Discover insightful articles written by talented law students.\",\n        creator: \"@afersata\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\n// Root layout for non-localized routes (admin, api, etc.)\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_Ethiopic_arguments_subsets_ethiopic_variable_font_ethiopic_display_swap_variableName_notoSansEthiopic___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-white dark:bg-gray-900 font-inter antialiased transition-colors duration-300\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/articles-list.tsx":
/*!******************************************!*\
  !*** ./src/components/articles-list.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ArticlesList: () => (/* binding */ ArticlesList)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ArticlesList = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ArticlesList() from the server but ArticlesList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\afersata\\lawblog\\src\\components\\articles-list.tsx",
"ArticlesList",
);

/***/ }),

/***/ "(rsc)/./src/components/blog-footer.tsx":
/*!****************************************!*\
  !*** ./src/components/blog-footer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BlogFooter: () => (/* binding */ BlogFooter)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const BlogFooter = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call BlogFooter() from the server but BlogFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\afersata\\lawblog\\src\\components\\blog-footer.tsx",
"BlogFooter",
);

/***/ }),

/***/ "(rsc)/./src/components/blog-navigation.tsx":
/*!********************************************!*\
  !*** ./src/components/blog-navigation.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BlogNavigation: () => (/* binding */ BlogNavigation)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const BlogNavigation = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call BlogNavigation() from the server but BlogNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\afersata\\lawblog\\src\\components\\blog-navigation.tsx",
"BlogNavigation",
);

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers),
/* harmony export */   useAuth: () => (/* binding */ useAuth),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\afersata\\lawblog\\src\\components\\providers.tsx",
"useAuth",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\afersata\\lawblog\\src\\components\\providers.tsx",
"useTheme",
);const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\afersata\\lawblog\\src\\components\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(rsc)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\afersata\\lawblog\\src\\components\\theme-provider.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\afersata\\lawblog\\src\\components\\theme-provider.tsx",
"useTheme",
);

/***/ }),

/***/ "(rsc)/./src/i18n/request.ts":
/*!*****************************!*\
  !*** ./src/i18n/request.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\");\n\n// Can be imported from a shared config\nconst locales = [\n    'en',\n    'am'\n];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that a valid locale is used\n    if (!locale || !locales.includes(locale)) {\n        locale = 'en';\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yZXF1ZXN0LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBR3BELHVDQUF1QztBQUN2QyxNQUFNQyxVQUFVO0lBQUM7SUFBTTtDQUFLO0FBRTVCLGlFQUFlRCw0REFBZ0JBLENBQUMsT0FBTyxFQUFFRSxhQUFhLEVBQUU7SUFDdEQsdURBQXVEO0lBQ3ZELElBQUlDLFNBQVMsTUFBTUQ7SUFFbkIscUNBQXFDO0lBQ3JDLElBQUksQ0FBQ0MsVUFBVSxDQUFDRixRQUFRRyxRQUFRLENBQUNELFNBQWdCO1FBQy9DQSxTQUFTO0lBQ1g7SUFFQSxPQUFPO1FBQ0xBO1FBQ0FFLFVBQVUsQ0FBQyxNQUFNLHlFQUFPLEdBQWdCLEVBQUVGLE9BQU8sTUFBTSxHQUFHRyxPQUFPO0lBQ25FO0FBQ0YsRUFBRSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEtyYWltYXRpY1xcRGVza3RvcFxcYWZlcnNhdGFcXGxhd2Jsb2dcXHNyY1xcaTE4blxccmVxdWVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRSZXF1ZXN0Q29uZmlnIH0gZnJvbSAnbmV4dC1pbnRsL3NlcnZlcic7XG5pbXBvcnQgeyBub3RGb3VuZCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5cbi8vIENhbiBiZSBpbXBvcnRlZCBmcm9tIGEgc2hhcmVkIGNvbmZpZ1xuY29uc3QgbG9jYWxlcyA9IFsnZW4nLCAnYW0nXTtcblxuZXhwb3J0IGRlZmF1bHQgZ2V0UmVxdWVzdENvbmZpZyhhc3luYyAoeyByZXF1ZXN0TG9jYWxlIH0pID0+IHtcbiAgLy8gVGhpcyB0eXBpY2FsbHkgY29ycmVzcG9uZHMgdG8gdGhlIGBbbG9jYWxlXWAgc2VnbWVudFxuICBsZXQgbG9jYWxlID0gYXdhaXQgcmVxdWVzdExvY2FsZTtcblxuICAvLyBFbnN1cmUgdGhhdCBhIHZhbGlkIGxvY2FsZSBpcyB1c2VkXG4gIGlmICghbG9jYWxlIHx8ICFsb2NhbGVzLmluY2x1ZGVzKGxvY2FsZSBhcyBhbnkpKSB7XG4gICAgbG9jYWxlID0gJ2VuJztcbiAgfVxuXG4gIHJldHVybiB7XG4gICAgbG9jYWxlLFxuICAgIG1lc3NhZ2VzOiAoYXdhaXQgaW1wb3J0KGAuLi8uLi9tZXNzYWdlcy8ke2xvY2FsZX0uanNvbmApKS5kZWZhdWx0XG4gIH07XG59KTtcbiJdLCJuYW1lcyI6WyJnZXRSZXF1ZXN0Q29uZmlnIiwibG9jYWxlcyIsInJlcXVlc3RMb2NhbGUiLCJsb2NhbGUiLCJpbmNsdWRlcyIsIm1lc3NhZ2VzIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/request.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/blog-service.ts":
/*!*********************************!*\
  !*** ./src/lib/blog-service.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCategories: () => (/* binding */ getCategories),\n/* harmony export */   getCategoryPosts: () => (/* binding */ getCategoryPosts),\n/* harmony export */   getPostBySlug: () => (/* binding */ getPostBySlug),\n/* harmony export */   getPublishedPosts: () => (/* binding */ getPublishedPosts),\n/* harmony export */   getWriterBySlug: () => (/* binding */ getWriterBySlug),\n/* harmony export */   getWriterPosts: () => (/* binding */ getWriterPosts),\n/* harmony export */   getWriters: () => (/* binding */ getWriters),\n/* harmony export */   searchPosts: () => (/* binding */ searchPosts)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://kupfufxsqrnwvvyuvtga.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.eag74xH4pvOKvFFAmrJLW8l4KV_KgIMTm0eVjew5tzA\");\nconst adminSupabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://kupfufxsqrnwvvyuvtga.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.eag74xH4pvOKvFFAmrJLW8l4KV_KgIMTm0eVjew5tzA\");\n// Public functions for blog readers\nasync function getPublishedPosts(limit = 10, offset = 0) {\n    const { data, error } = await supabase.from('posts').select(`\n      *,\n      writer:writers(*),\n      category:categories(*),\n      tags:post_tags(tag:tags(*))\n    `).eq('status', 'published').order('published_at', {\n        ascending: false\n    }).range(offset, offset + limit - 1);\n    if (error) {\n        console.error('Error fetching posts:', error);\n        return [];\n    }\n    return data.map(formatPost);\n}\nasync function getPostBySlug(slug) {\n    const { data, error } = await supabase.from('posts').select(`\n      *,\n      writer:writers(*),\n      category:categories(*),\n      tags:post_tags(tag:tags(*))\n    `).eq('slug', slug).eq('status', 'published').single();\n    if (error || !data) {\n        return null;\n    }\n    // Increment view count\n    await supabase.rpc('increment_post_views', {\n        post_id: data.id\n    });\n    return formatPost(data);\n}\nasync function getWriters() {\n    const { data, error } = await supabase.from('writers').select('*').eq('status', 'active').order('total_posts', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching writers:', error);\n        return [];\n    }\n    return data.map(formatWriter);\n}\nasync function getWriterBySlug(slug) {\n    // For now, we'll use the writer's name as slug (you can add a slug field later)\n    const { data, error } = await supabase.from('writers').select('*').eq('status', 'active').ilike('name', `%${slug.replace('-', ' ')}%`).single();\n    if (error || !data) {\n        return null;\n    }\n    return formatWriter(data);\n}\nasync function getWriterPosts(writerId) {\n    const { data, error } = await supabase.from('posts').select(`\n      *,\n      writer:writers(*),\n      category:categories(*),\n      tags:post_tags(tag:tags(*))\n    `).eq('writer_id', writerId).eq('status', 'published').order('published_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching writer posts:', error);\n        return [];\n    }\n    return data.map(formatPost);\n}\nasync function getCategories() {\n    const { data, error } = await supabase.from('categories').select('*').order('post_count', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching categories:', error);\n        return [];\n    }\n    return data.map(formatCategory);\n}\nasync function getCategoryPosts(categorySlug) {\n    const { data, error } = await supabase.from('posts').select(`\n      *,\n      writer:writers(*),\n      category:categories(*),\n      tags:post_tags(tag:tags(*))\n    `).eq('category.slug', categorySlug).eq('status', 'published').order('published_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching category posts:', error);\n        return [];\n    }\n    return data.map(formatPost);\n}\nasync function searchPosts(query) {\n    const { data, error } = await supabase.from('posts').select(`\n      *,\n      writer:writers(*),\n      category:categories(*),\n      tags:post_tags(tag:tags(*))\n    `).eq('status', 'published').or(`title.ilike.%${query}%,content.ilike.%${query}%,excerpt.ilike.%${query}%`).order('published_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error searching posts:', error);\n        return [];\n    }\n    return data.map(formatPost);\n}\n// Helper functions\nfunction formatPost(data) {\n    return {\n        id: data.id,\n        title: data.title,\n        slug: data.slug,\n        excerpt: data.excerpt,\n        content: data.content,\n        featuredImage: data.featured_image,\n        status: data.status,\n        writerId: data.writer_id,\n        categoryId: data.category_id,\n        readingTime: data.reading_time,\n        viewCount: data.view_count,\n        publishedAt: data.published_at,\n        createdAt: data.created_at,\n        updatedAt: data.updated_at,\n        writer: data.writer ? formatWriter(data.writer) : undefined,\n        category: data.category ? formatCategory(data.category) : undefined,\n        tags: data.tags ? data.tags.map((t)=>formatTag(t.tag)) : []\n    };\n}\nfunction formatWriter(data) {\n    return {\n        id: data.id,\n        name: data.name,\n        email: data.email,\n        bio: data.bio,\n        avatarUrl: data.avatar_url,\n        university: data.university,\n        graduationYear: data.graduation_year,\n        specialization: data.specialization,\n        socialLinks: data.social_links || {},\n        status: data.status,\n        totalPosts: data.total_posts,\n        totalViews: data.total_views,\n        joinedDate: data.joined_date\n    };\n}\nfunction formatCategory(data) {\n    return {\n        id: data.id,\n        name: data.name,\n        slug: data.slug,\n        description: data.description,\n        color: data.color,\n        postCount: data.post_count\n    };\n}\nfunction formatTag(data) {\n    return {\n        id: data.id,\n        name: data.name,\n        slug: data.slug,\n        postCount: data.post_count\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/blog-service.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Ethiopic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22ethiopic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ethiopic%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansEthiopic%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Ethiopic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22ethiopic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ethiopic%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansEthiopic%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Ethiopic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22ethiopic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ethiopic%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansEthiopic%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Ethiopic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22ethiopic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ethiopic%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansEthiopic%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(ssr)/./src/components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Ethiopic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22ethiopic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ethiopic%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansEthiopic%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Carticles-list.tsx%22%2C%22ids%22%3A%5B%22ArticlesList%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-footer.tsx%22%2C%22ids%22%3A%5B%22BlogFooter%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-navigation.tsx%22%2C%22ids%22%3A%5B%22BlogNavigation%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Carticles-list.tsx%22%2C%22ids%22%3A%5B%22ArticlesList%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-footer.tsx%22%2C%22ids%22%3A%5B%22BlogFooter%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-navigation.tsx%22%2C%22ids%22%3A%5B%22BlogNavigation%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/articles-list.tsx */ \"(ssr)/./src/components/articles-list.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/blog-footer.tsx */ \"(ssr)/./src/components/blog-footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/blog-navigation.tsx */ \"(ssr)/./src/components/blog-navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0tyYWltYXRpYyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZmVyc2F0YSU1QyU1Q2xhd2Jsb2clNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYXJ0aWNsZXMtbGlzdC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBcnRpY2xlc0xpc3QlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDS3JhaW1hdGljJTVDJTVDRGVza3RvcCU1QyU1Q2FmZXJzYXRhJTVDJTVDbGF3YmxvZyU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNibG9nLWZvb3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJCbG9nRm9vdGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0tyYWltYXRpYyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZmVyc2F0YSU1QyU1Q2xhd2Jsb2clNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYmxvZy1uYXZpZ2F0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkJsb2dOYXZpZ2F0aW9uJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBMko7QUFDM0o7QUFDQSw0S0FBdUo7QUFDdko7QUFDQSxvTEFBK0oiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkFydGljbGVzTGlzdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEtyYWltYXRpY1xcXFxEZXNrdG9wXFxcXGFmZXJzYXRhXFxcXGxhd2Jsb2dcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcYXJ0aWNsZXMtbGlzdC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkJsb2dGb290ZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxLcmFpbWF0aWNcXFxcRGVza3RvcFxcXFxhZmVyc2F0YVxcXFxsYXdibG9nXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGJsb2ctZm9vdGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQmxvZ05hdmlnYXRpb25cIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxLcmFpbWF0aWNcXFxcRGVza3RvcFxcXFxhZmVyc2F0YVxcXFxsYXdibG9nXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGJsb2ctbmF2aWdhdGlvbi50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Carticles-list.tsx%22%2C%22ids%22%3A%5B%22ArticlesList%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-footer.tsx%22%2C%22ids%22%3A%5B%22BlogFooter%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cafersata%5C%5Clawblog%5C%5Csrc%5C%5Ccomponents%5C%5Cblog-navigation.tsx%22%2C%22ids%22%3A%5B%22BlogNavigation%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/articles-list.tsx":
/*!******************************************!*\
  !*** ./src/components/articles-list.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArticlesList: () => (/* binding */ ArticlesList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Eye,Filter,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Eye,Filter,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Eye,Filter,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Eye,Filter,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Eye,Filter,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Eye,Filter,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ ArticlesList auto */ \n\n\n\n\n\nfunction ArticlesList({ posts, categories }) {\n    const [filteredPosts, setFilteredPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(posts);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const locale = params.locale;\n    const handleCategoryFilter = (categoryId)=>{\n        setSelectedCategory(categoryId);\n        if (categoryId === 'all') {\n            setFilteredPosts(posts);\n        } else {\n            setFilteredPosts(posts.filter((post)=>post.categoryId === categoryId));\n        }\n    };\n    const handleSearch = (query)=>{\n        setSearchQuery(query);\n        const filtered = posts.filter((post)=>post.title.toLowerCase().includes(query.toLowerCase()) || post.excerpt?.toLowerCase().includes(query.toLowerCase()) || post.writer?.name.toLowerCase().includes(query.toLowerCase()));\n        setFilteredPosts(filtered);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex-1 max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search articles...\",\n                                    value: searchQuery,\n                                    onChange: (e)=>handleSearch(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedCategory,\n                                    onChange: (e)=>handleCategoryFilter(e.target.value),\n                                    className: \"px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"All Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category.id,\n                                                children: category.name\n                                            }, category.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8\",\n                children: filteredPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                        className: \"bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 group\",\n                        children: [\n                            post.featuredImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-48 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        src: post.featuredImage,\n                                        alt: post.title,\n                                        fill: true,\n                                        className: \"object-cover group-hover:scale-105 transition-transform duration-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, this),\n                                    post.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-4 left-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 text-xs font-semibold text-white rounded-full\",\n                                            style: {\n                                                backgroundColor: post.category.color\n                                            },\n                                            children: post.category.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-48 bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl text-blue-200\",\n                                        children: \"\\uD83D\\uDCDA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this),\n                                    post.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-4 left-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 text-xs font-semibold text-white rounded-full\",\n                                            style: {\n                                                backgroundColor: post.category.color\n                                            },\n                                            children: post.category.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-sm text-gray-500 mb-3\",\n                                        children: [\n                                            post.writer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: post.writer.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            post.readingTime,\n                                                            \" min read\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: post.viewCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: `/${locale}/articles/${post.slug}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2\",\n                                            children: post.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this),\n                                    post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4 line-clamp-3\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Eye_Filter_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: formatDate(post.publishedAt || post.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: `/${locale}/articles/${post.slug}`,\n                                                className: \"text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors\",\n                                                children: \"Read More →\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, post.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            filteredPosts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"No articles found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: searchQuery || selectedCategory !== 'all' ? 'Try adjusting your search or filter criteria.' : 'Check back soon for new articles from our talented writers.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this),\n            filteredPosts.length > 0 && filteredPosts.length >= 20 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105\",\n                    children: \"Load More Articles\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n                lineNumber: 196,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\articles-list.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/articles-list.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/blog-footer.tsx":
/*!****************************************!*\
  !*** ./src/components/blog-footer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogFooter: () => (/* binding */ BlogFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ BlogFooter auto */ \n\n\n\n\nfunction BlogFooter() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const locale = params.locale;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)();\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        explore: [\n            {\n                name: t('navigation.articles'),\n                href: `/${locale}/articles`\n            },\n            {\n                name: t('navigation.writers'),\n                href: `/${locale}/writers`\n            },\n            {\n                name: t('navigation.categories'),\n                href: `/${locale}/categories`\n            }\n        ],\n        legal: [\n            {\n                name: 'Constitutional Law',\n                href: `/${locale}/categories/constitutional-law`\n            },\n            {\n                name: 'Criminal Law',\n                href: `/${locale}/categories/criminal-law`\n            },\n            {\n                name: 'Corporate Law',\n                href: `/${locale}/categories/corporate-law`\n            },\n            {\n                name: 'Environmental Law',\n                href: `/${locale}/categories/environmental-law`\n            }\n        ],\n        about: [\n            {\n                name: `${t('footer.about')} ${t('common.appName')}`,\n                href: `/${locale}/about`\n            },\n            {\n                name: t('navigation.contact'),\n                href: `/${locale}/contact`\n            },\n            {\n                name: 'Privacy Policy',\n                href: `/${locale}/privacy`\n            },\n            {\n                name: 'Terms of Service',\n                href: `/${locale}/terms`\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 dark:bg-gray-950 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Globe_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold font-ethiopic\",\n                                            children: t('common.appName')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 dark:text-gray-500 mb-6 leading-relaxed\",\n                                    children: t('footer.description')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 text-sm text-gray-400 dark:text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Globe_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('footer.availableIn')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t('footer.explore')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.explore.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 dark:text-gray-500 hover:text-white dark:hover:text-gray-300 transition-colors\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t('footer.legalTopics')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 dark:text-gray-500 hover:text-white dark:hover:text-gray-300 transition-colors\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t('footer.about')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.about.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 dark:text-gray-500 hover:text-white dark:hover:text-gray-300 transition-colors\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 pt-6 border-t border-gray-800 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-400 dark:text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Globe_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 pt-8 border-t border-gray-800 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 text-gray-400 dark:text-gray-500 text-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('footer.copyright')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm text-gray-400 dark:text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: t('footer.poweredBy')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/en\",\n                                                className: `px-2 py-1 rounded transition-colors ${locale === 'en' ? 'bg-blue-600 text-white' : 'hover:text-white dark:hover:text-gray-300'}`,\n                                                children: \"English\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/am\",\n                                                className: `px-2 py-1 rounded transition-colors font-ethiopic ${locale === 'am' ? 'bg-blue-600 text-white' : 'hover:text-white dark:hover:text-gray-300'}`,\n                                                children: \"አማርኛ\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-footer.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/blog-footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/blog-navigation.tsx":
/*!********************************************!*\
  !*** ./src/components/blog-navigation.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogNavigation: () => (/* binding */ BlogNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(ssr)/./src/components/theme-provider.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FolderOpen,Menu,Moon,Search,Sun,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FolderOpen,Menu,Moon,Search,Sun,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FolderOpen,Menu,Moon,Search,Sun,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FolderOpen,Menu,Moon,Search,Sun,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FolderOpen,Menu,Moon,Search,Sun,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FolderOpen,Menu,Moon,Search,Sun,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FolderOpen,Menu,Moon,Search,Sun,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FolderOpen,Menu,Moon,Search,Sun,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ BlogNavigation auto */ \n\n\n\n\n\n\nfunction BlogNavigation() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const locale = params.locale;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const { theme, toggleTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BlogNavigation.useEffect\": ()=>{\n            const handleScroll = {\n                \"BlogNavigation.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 10);\n                }\n            }[\"BlogNavigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"BlogNavigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"BlogNavigation.useEffect\"];\n        }\n    }[\"BlogNavigation.useEffect\"], []);\n    const navigation = [\n        {\n            name: t('navigation.home'),\n            href: `/${locale}`,\n            icon: _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: t('navigation.articles'),\n            href: `/${locale}/articles`,\n            icon: _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: t('navigation.writers'),\n            href: `/${locale}/writers`,\n            icon: _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: t('navigation.categories'),\n            href: `/${locale}/categories`,\n            icon: _barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === `/${locale}`) {\n            return pathname === href;\n        }\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700' : 'bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: `/${locale}`,\n                            className: \"flex items-center space-x-2 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent font-ethiopic\",\n                                    children: t('common.appName')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `relative px-3 py-2 text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? 'text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400'}`,\n                                    children: [\n                                        item.name,\n                                        isActive(item.href) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-x-0 -bottom-1 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"hidden sm:flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden lg:inline\",\n                                            children: t('common.search')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: toggleTheme,\n                                    className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\",\n                                    title: theme === 'dark' ? t('navigation.lightMode') : t('navigation.darkMode'),\n                                    children: theme === 'dark' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/en\",\n                                            className: `px-2 py-1 text-xs font-medium rounded transition-colors ${locale === 'en' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400'}`,\n                                            children: \"EN\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/am\",\n                                            className: `px-2 py-1 text-xs font-medium rounded transition-colors font-ethiopic ${locale === 'am' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400'}`,\n                                            children: \"አማ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setIsOpen(!isOpen),\n                                    className: \"md:hidden p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\",\n                                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t border-gray-200 dark:border-gray-700 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    onClick: ()=>setIsOpen(false),\n                                    className: `flex items-center space-x-3 px-3 py-3 text-base font-medium rounded-lg transition-colors ${isActive(item.href) ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/50' : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"flex items-center space-x-3 w-full px-3 py-3 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: t('common.search')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: toggleTheme,\n                                className: \"flex items-center space-x-3 w-full px-3 py-3 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors\",\n                                children: theme === 'dark' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t('navigation.lightMode')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FolderOpen_Menu_Moon_Search_Sun_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t('navigation.darkMode')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\blog-navigation.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/blog-navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,useTheme,Providers auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createClientSupabase)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session } } = await supabase.auth.getSession();\n                    setUser(session?.user ?? null);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    setUser(session?.user ?? null);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        supabase.auth\n    ]);\n    const signOut = async ()=>{\n        await supabase.auth.signOut();\n    };\n    const value = {\n        user,\n        loading,\n        signOut\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            // Get theme from localStorage or system preference\n            const savedTheme = localStorage.getItem('theme');\n            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n            const initialTheme = savedTheme || systemTheme;\n            setTheme(initialTheme);\n            document.documentElement.classList.toggle('dark', initialTheme === 'dark');\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    const toggleTheme = ()=>{\n        const newTheme = theme === 'light' ? 'dark' : 'light';\n        setTheme(newTheme);\n        localStorage.setItem('theme', newTheme);\n        document.documentElement.classList.toggle('dark', newTheme === 'dark');\n    };\n    const value = {\n        theme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dark') // Default to dark mode\n    ;\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setMounted(true);\n            // Check for saved theme preference or default to dark\n            const savedTheme = localStorage.getItem('theme');\n            if (savedTheme) {\n                setThemeState(savedTheme);\n            } else {\n                // Default to dark mode\n                setThemeState('dark');\n                localStorage.setItem('theme', 'dark');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (mounted) {\n                const root = window.document.documentElement;\n                root.classList.remove('light', 'dark');\n                root.classList.add(theme);\n                localStorage.setItem('theme', theme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setThemeState((prev)=>prev === 'dark' ? 'light' : 'dark');\n    };\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"dark\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\theme-provider.tsx\",\n            lineNumber: 51,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme,\n            setTheme\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: theme,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\theme-provider.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: 'dark',\n            toggleTheme: ()=>{},\n            setTheme: ()=>{}\n        };\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://kupfufxsqrnwvvyuvtga.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.eag74xH4pvOKvFFAmrJLW8l4KV_KgIMTm0eVjew5tzA\";\n// Client-side Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Client component client\nconst createClientSupabase = ()=>(0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/@formatjs","vendor-chunks/whatwg-url","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/react-hot-toast","vendor-chunks/lucide-react","vendor-chunks/next-intl","vendor-chunks/webidl-conversions","vendor-chunks/goober","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Farticles%2Fpage&page=%2F%5Blocale%5D%2Farticles%2Fpage&appPaths=%2F%5Blocale%5D%2Farticles%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Farticles%2Fpage.tsx&appDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5Cafersata%5Clawblog%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKraimatic%5CDesktop%5Cafersata%5Clawblog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();