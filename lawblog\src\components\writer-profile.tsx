'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import { MapPin, GraduationCap, BookOpen, Eye, Calendar, ExternalLink, Clock, User } from 'lucide-react'
import { Writer, Post } from '@/lib/blog-service'

interface WriterProfileProps {
  writer: Writer
  posts: Post[]
}

export function WriterProfile({ writer, posts }: WriterProfileProps) {
  const params = useParams()
  const locale = params.locale as string

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatJoinDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    })
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Writer Header */}
      <div className="bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-200 mb-8">
        {/* Cover */}
        <div className="relative h-48 sm:h-64 bg-gradient-to-br from-blue-500 to-purple-600">
          <div className="absolute inset-0 bg-black/20"></div>
          
          {/* Profile Info */}
          <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8">
            <div className="flex flex-col sm:flex-row sm:items-end space-y-4 sm:space-y-0 sm:space-x-6">
              {/* Avatar */}
              {writer.avatarUrl ? (
                <div className="relative w-24 h-24 sm:w-32 sm:h-32 rounded-full overflow-hidden border-4 border-white shadow-lg">
                  <Image
                    src={writer.avatarUrl}
                    alt={writer.name}
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="w-24 h-24 sm:w-32 sm:h-32 rounded-full bg-white border-4 border-white shadow-lg flex items-center justify-center">
                  <span className="text-3xl sm:text-4xl font-bold text-gray-600">
                    {writer.name.charAt(0)}
                  </span>
                </div>
              )}

              {/* Info */}
              <div className="flex-1">
                <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">{writer.name}</h1>
                {writer.specialization && (
                  <p className="text-white/90 text-lg mb-2">{writer.specialization}</p>
                )}
                <div className="flex flex-wrap items-center gap-4 text-white/80 text-sm">
                  {writer.university && (
                    <div className="flex items-center space-x-1">
                      <GraduationCap className="w-4 h-4" />
                      <span>{writer.university}</span>
                      {writer.graduationYear && (
                        <span>• Class of {writer.graduationYear}</span>
                      )}
                    </div>
                  )}
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>Joined {formatJoinDate(writer.joinedDate)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="p-6 sm:p-8">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-xl">
              <div className="flex items-center justify-center space-x-1 text-blue-600 mb-1">
                <BookOpen className="w-5 h-5" />
                <span className="font-bold text-2xl">{writer.totalPosts}</span>
              </div>
              <span className="text-sm text-gray-600">Articles</span>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-xl">
              <div className="flex items-center justify-center space-x-1 text-purple-600 mb-1">
                <Eye className="w-5 h-5" />
                <span className="font-bold text-2xl">{writer.totalViews.toLocaleString()}</span>
              </div>
              <span className="text-sm text-gray-600">Total Views</span>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-xl">
              <div className="flex items-center justify-center space-x-1 text-green-600 mb-1">
                <Clock className="w-5 h-5" />
                <span className="font-bold text-2xl">{Math.round(posts.reduce((acc, post) => acc + post.readingTime, 0) / posts.length) || 0}</span>
              </div>
              <span className="text-sm text-gray-600">Avg. Read Time</span>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-xl">
              <div className="flex items-center justify-center space-x-1 text-orange-600 mb-1">
                <User className="w-5 h-5" />
                <span className="font-bold text-2xl">{Math.round(writer.totalViews / Math.max(writer.totalPosts, 1))}</span>
              </div>
              <span className="text-sm text-gray-600">Avg. Views</span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* About Section */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 mb-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">About</h2>
            {writer.bio ? (
              <p className="text-gray-600 leading-relaxed">{writer.bio}</p>
            ) : (
              <p className="text-gray-500 italic">No bio available yet.</p>
            )}

            {/* Social Links */}
            {writer.socialLinks && Object.keys(writer.socialLinks).length > 0 && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-semibold text-gray-900 mb-3">Connect</h3>
                <div className="flex space-x-3">
                  {Object.entries(writer.socialLinks).map(([platform, url]) => (
                    <a
                      key={platform}
                      href={url as string}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors bg-gray-50 rounded-lg hover:bg-blue-50"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Articles Section */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Articles by {writer.name}</h2>
            
            {posts.length > 0 ? (
              <div className="space-y-6">
                {posts.map((post) => (
                  <article
                    key={post.id}
                    className="border-b border-gray-200 last:border-b-0 pb-6 last:pb-0"
                  >
                    <div className="flex flex-col sm:flex-row sm:space-x-4">
                      {/* Featured Image */}
                      {post.featuredImage ? (
                        <div className="relative w-full sm:w-32 h-32 sm:h-24 rounded-lg overflow-hidden mb-4 sm:mb-0 flex-shrink-0">
                          <Image
                            src={post.featuredImage}
                            alt={post.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                      ) : (
                        <div className="w-full sm:w-32 h-32 sm:h-24 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center mb-4 sm:mb-0 flex-shrink-0">
                          <BookOpen className="w-8 h-8 text-blue-300" />
                        </div>
                      )}

                      {/* Content */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                          {post.category && (
                            <span
                              className="px-2 py-1 text-xs font-semibold text-white rounded"
                              style={{ backgroundColor: post.category.color }}
                            >
                              {post.category.name}
                            </span>
                          )}
                          <span>{formatDate(post.publishedAt || post.createdAt)}</span>
                          <span>•</span>
                          <span>{post.readingTime} min read</span>
                          <span>•</span>
                          <span>{post.viewCount} views</span>
                        </div>

                        <Link href={`/${locale}/articles/${post.slug}`}>
                          <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors mb-2 line-clamp-2">
                            {post.title}
                          </h3>
                        </Link>

                        {post.excerpt && (
                          <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                            {post.excerpt}
                          </p>
                        )}

                        <Link
                          href={`/${locale}/articles/${post.slug}`}
                          className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors"
                        >
                          Read Article →
                        </Link>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <BookOpen className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No articles yet</h3>
                <p className="text-gray-600">
                  {writer.name} hasn't published any articles yet. Check back soon!
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
