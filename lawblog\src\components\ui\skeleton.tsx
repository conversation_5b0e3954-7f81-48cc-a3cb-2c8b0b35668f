import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  variant?: 'default' | 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

export function Skeleton({ 
  className, 
  variant = 'default',
  width,
  height,
  lines = 1,
  ...props 
}: SkeletonProps) {
  const baseClasses = 'skeleton animate-pulse bg-gray-200 dark:bg-gray-700';
  
  const variantClasses = {
    default: 'rounded-md',
    text: 'rounded h-4',
    circular: 'rounded-full',
    rectangular: 'rounded-none'
  };

  const style = {
    width: width || '100%',
    height: height || (variant === 'text' ? '1rem' : '1.5rem')
  };

  if (variant === 'text' && lines > 1) {
    return (
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(baseClasses, variantClasses[variant], className)}
            style={{
              ...style,
              width: index === lines - 1 ? '75%' : '100%'
            }}
            {...props}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn(baseClasses, variantClasses[variant], className)}
      style={style}
      {...props}
    />
  );
}

// Predefined skeleton components for common use cases
export function ArticleCardSkeleton() {
  return (
    <div className="glass-card p-6 space-y-4">
      <Skeleton variant="rectangular" height="200px" />
      <div className="space-y-2">
        <Skeleton variant="text" width="60%" />
        <Skeleton variant="text" lines={3} />
      </div>
      <div className="flex items-center space-x-4">
        <Skeleton variant="circular" width="40px" height="40px" />
        <div className="space-y-1">
          <Skeleton variant="text" width="120px" />
          <Skeleton variant="text" width="80px" />
        </div>
      </div>
    </div>
  );
}

export function WriterCardSkeleton() {
  return (
    <div className="glass-card p-6 text-center space-y-4">
      <Skeleton variant="circular" width="80px" height="80px" className="mx-auto" />
      <div className="space-y-2">
        <Skeleton variant="text" width="150px" className="mx-auto" />
        <Skeleton variant="text" width="120px" className="mx-auto" />
        <Skeleton variant="text" lines={2} />
      </div>
      <Skeleton variant="rectangular" height="40px" width="120px" className="mx-auto" />
    </div>
  );
}

export function NavigationSkeleton() {
  return (
    <div className="flex items-center justify-between p-4">
      <Skeleton variant="text" width="120px" height="32px" />
      <div className="flex space-x-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton key={index} variant="text" width="80px" height="24px" />
        ))}
      </div>
    </div>
  );
}
