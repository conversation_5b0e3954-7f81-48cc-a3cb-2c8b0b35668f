'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Calendar, User, Clock, Eye, ArrowRight, Tag } from 'lucide-react';
import { cn } from '@/lib/utils';
import { BookmarkButton } from './bookmark';
import { SocialShare } from './social-share';
import { FadeIn } from './page-transition';

interface Article {
  id: string;
  title: string;
  excerpt: string;
  content?: string;
  image?: string;
  author: {
    name: string;
    avatar?: string;
    university?: string;
  };
  category: string;
  publishedAt: string;
  readingTime: number;
  views?: number;
  tags?: string[];
}

interface ArticleCardProps {
  article: Article;
  variant?: 'default' | 'featured' | 'compact' | 'minimal';
  className?: string;
  showBookmark?: boolean;
  showShare?: boolean;
  locale?: string;
}

export function ArticleCard({ 
  article, 
  variant = 'default',
  className,
  showBookmark = true,
  showShare = false,
  locale = 'en'
}: ArticleCardProps) {
  const t = useTranslations();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  const articleUrl = typeof window !== 'undefined'
    ? `${window.location.origin}/${locale}/articles/${article.id}`
    : `/${locale}/articles/${article.id}`;

  if (variant === 'minimal') {
    return (
      <FadeIn>
        <Link href={`/${locale}/articles/${article.id}`}>
          <article className={cn(
            "group p-4 rounded-xl transition-all duration-300",
            "hover:bg-gray-50 dark:hover:bg-gray-800/50",
            "border border-transparent hover:border-gray-200 dark:hover:border-gray-700",
            className
          )}>
            <div className="flex items-start space-x-4">
              {article.image && (
                <div className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden">
                  <Image
                    src={article.image}
                    alt={article.title}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-gray-900 dark:text-white line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                  {article.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {formatDate(article.publishedAt)} • {article.readingTime} min read
                </p>
              </div>
            </div>
          </article>
        </Link>
      </FadeIn>
    );
  }

  if (variant === 'compact') {
    return (
      <FadeIn>
        <article 
          className={cn(
            "glass-card p-4 transition-all duration-300 hover-lift card-hover",
            className
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="flex items-start justify-between mb-3">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              <Tag className="w-3 h-3 mr-1" />
              {article.category}
            </span>
            {showBookmark && (
              <BookmarkButton 
                articleId={article.id} 
                articleTitle={article.title}
                size="sm"
              />
            )}
          </div>

          <Link href={`/${locale}/articles/${article.id}`}>
            <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2 line-clamp-2 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
              {article.title}
            </h3>
          </Link>

          <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4">
            {article.excerpt}
          </p>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(article.publishedAt)}</span>
              <Clock className="w-3 h-3 ml-2" />
              <span>{article.readingTime} min</span>
            </div>

            <Link 
              href={`/${locale}/articles/${article.id}`}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200"
            >
              <ArrowRight className={cn(
                "w-4 h-4 transition-transform duration-300",
                isHovered && "translate-x-1"
              )} />
            </Link>
          </div>
        </article>
      </FadeIn>
    );
  }

  // Default and featured variants
  const isFeatured = variant === 'featured';

  return (
    <FadeIn>
      <article 
        className={cn(
          "glass-card overflow-hidden transition-all duration-300 hover-lift card-hover",
          isFeatured ? "lg:flex lg:items-center" : "",
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Image */}
        {article.image && (
          <div className={cn(
            "relative overflow-hidden",
            isFeatured ? "lg:w-1/2 h-64 lg:h-80" : "h-48"
          )}>
            <div className={cn(
              "absolute inset-0 bg-gradient-to-t from-black/20 to-transparent z-10",
              !imageLoaded && "animate-pulse bg-gray-200 dark:bg-gray-700"
            )} />
            
            <Image
              src={article.image}
              alt={article.title}
              fill
              className={cn(
                "object-cover transition-all duration-500",
                isHovered && "scale-110",
                imageLoaded ? "opacity-100" : "opacity-0"
              )}
              onLoad={() => setImageLoaded(true)}
            />

            {/* Category badge */}
            <div className="absolute top-4 left-4 z-20">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-800 backdrop-blur-sm">
                <Tag className="w-3 h-3 mr-1" />
                {article.category}
              </span>
            </div>

            {/* Bookmark button */}
            {showBookmark && (
              <div className="absolute top-4 right-4 z-20">
                <BookmarkButton 
                  articleId={article.id} 
                  articleTitle={article.title}
                  className="bg-white/90 backdrop-blur-sm"
                />
              </div>
            )}
          </div>
        )}

        {/* Content */}
        <div className={cn(
          "p-6",
          isFeatured ? "lg:w-1/2 lg:p-8" : ""
        )}>
          <Link href={`/${locale}/articles/${article.id}`}>
            <h2 className={cn(
              "font-bold text-gray-900 dark:text-white mb-3 line-clamp-2 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200",
              isFeatured ? "text-2xl lg:text-3xl" : "text-xl"
            )}>
              {article.title}
            </h2>
          </Link>

          <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
            {article.excerpt}
          </p>

          {/* Tags */}
          {article.tags && article.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {article.tags.slice(0, 3).map((tag, index) => (
                <span 
                  key={index}
                  className="px-2 py-1 text-xs rounded-md bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400"
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}

          {/* Author and meta */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {article.author.avatar && (
                <Image
                  src={article.author.avatar}
                  alt={article.author.name}
                  width={40}
                  height={40}
                  className="rounded-full"
                />
              )}
              <div>
                <p className="font-medium text-gray-900 dark:text-white text-sm">
                  {article.author.name}
                </p>
                {article.author.university && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {article.author.university}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(article.publishedAt)}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{article.readingTime} min</span>
              </div>
              {article.views && (
                <div className="flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span>{article.views}</span>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Link 
              href={`/${locale}/articles/${article.id}`}
              className="inline-flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
            >
              <span>{t('Articles.readMore')}</span>
              <ArrowRight className={cn(
                "w-4 h-4 transition-transform duration-300",
                isHovered && "translate-x-1"
              )} />
            </Link>

            {showShare && (
              <SocialShare 
                url={articleUrl}
                title={article.title}
                description={article.excerpt}
                variant="button"
                size="sm"
              />
            )}
          </div>
        </div>
      </article>
    </FadeIn>
  );
}
