'use client';

import { Suspense, useState } from 'react';
import { useTranslations } from 'next-intl';
import { BlogNavigation } from '@/components/blog-navigation';
import { BlogFooter } from '@/components/blog-footer';
import { ReadingProgress } from '@/components/ui/reading-progress';
import { SearchComponent } from '@/components/ui/search';
import { CategoryCard, CategoryStats, TrendingCategories } from '@/components/ui/category-card';
import { FadeIn, StaggeredList } from '@/components/ui/page-transition';
import { ArticleCardSkeleton } from '@/components/ui/skeleton';
import { Grid, List, TrendingUp, BookOpen } from 'lucide-react';

// Mock data for demonstration - replace with actual data fetching
const mockCategories = [
  {
    id: 'constitutional',
    name: 'Constitutional Law',
    description: 'Fundamental principles of government structure, individual rights, and the interpretation of constitutional provisions.',
    articlesCount: 45,
    writersCount: 12,
    totalViews: 25400,
    color: '#3B82F6',
    icon: 'constitutional',
    featured: true,
    trending: true
  },
  {
    id: 'criminal',
    name: 'Criminal Law',
    description: 'Criminal justice system, criminal procedure, sentencing, and criminal defense strategies.',
    articlesCount: 38,
    writersCount: 15,
    totalViews: 22100,
    color: '#EF4444',
    icon: 'criminal',
    featured: true,
    trending: true
  },
  {
    id: 'civil',
    name: 'Civil Law',
    description: 'Civil procedure, torts, contracts, property law, and civil litigation practices.',
    articlesCount: 52,
    writersCount: 18,
    totalViews: 28900,
    color: '#10B981',
    icon: 'civil',
    featured: true,
    trending: false
  },
  {
    id: 'commercial',
    name: 'Commercial Law',
    description: 'Business transactions, commercial contracts, banking law, and commercial dispute resolution.',
    articlesCount: 29,
    writersCount: 10,
    totalViews: 18600,
    color: '#F59E0B',
    icon: 'commercial',
    featured: false,
    trending: true
  },
  {
    id: 'international',
    name: 'International Law',
    description: 'International treaties, diplomatic relations, international trade, and cross-border legal issues.',
    articlesCount: 34,
    writersCount: 14,
    totalViews: 21300,
    color: '#8B5CF6',
    icon: 'international',
    featured: false,
    trending: false
  },
  {
    id: 'environmental',
    name: 'Environmental Law',
    description: 'Environmental protection, climate change law, natural resources, and sustainability regulations.',
    articlesCount: 26,
    writersCount: 8,
    totalViews: 15800,
    color: '#059669',
    icon: 'environmental',
    featured: false,
    trending: true
  },
  {
    id: 'corporate',
    name: 'Corporate Law',
    description: 'Corporate governance, securities regulation, mergers and acquisitions, and business compliance.',
    articlesCount: 31,
    writersCount: 11,
    totalViews: 19200,
    color: '#DC2626',
    icon: 'corporate',
    featured: false,
    trending: false
  },
  {
    id: 'human-rights',
    name: 'Human Rights Law',
    description: 'International human rights, civil liberties, discrimination law, and social justice advocacy.',
    articlesCount: 28,
    writersCount: 9,
    totalViews: 17500,
    color: '#EC4899',
    icon: 'human-rights',
    featured: false,
    trending: true
  }
];

export default function CategoriesPage() {
  const t = useTranslations();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filteredCategories, setFilteredCategories] = useState(mockCategories);

  const handleSearch = (filters: any) => {
    console.log('Search filters:', filters);
    // Implement filtering logic here
    // For now, just log the filters
  };

  const featuredCategories = filteredCategories.filter(cat => cat.featured);
  const regularCategories = filteredCategories.filter(cat => !cat.featured);
  const trendingCategories = filteredCategories.filter(cat => cat.trending);

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
      <ReadingProgress />
      <BlogNavigation />

      <main className="pt-16">
        {/* Header Section */}
        <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <FadeIn>
              <div className="text-center mb-12">
                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                  <span className="gradient-text">{t('Categories.title')}</span>
                </h1>
                <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                  {t('Categories.subtitle')}
                </p>
              </div>
            </FadeIn>

            {/* Stats */}
            <FadeIn delay={200}>
              <CategoryStats categories={filteredCategories} className="max-w-4xl mx-auto mb-12" />
            </FadeIn>

            {/* Search */}
            <FadeIn delay={400}>
              <div className="max-w-2xl mx-auto">
                <SearchComponent
                  onSearch={handleSearch}
                  placeholder="Search categories by name or topic..."
                  showFilters={false}
                />
              </div>
            </FadeIn>
          </div>
        </section>

        {/* Featured Categories */}
        {featuredCategories.length > 0 && (
          <section className="py-16 bg-gray-50 dark:bg-gray-800/50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <FadeIn>
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    Featured Categories
                  </h2>
                  <p className="text-lg text-gray-600 dark:text-gray-400">
                    Our most popular and comprehensive legal specializations
                  </p>
                </div>
              </FadeIn>

              <StaggeredList
                delay={150}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {featuredCategories.map((category) => (
                  <CategoryCard
                    key={category.id}
                    category={category}
                    variant="featured"
                    locale="en"
                  />
                ))}
              </StaggeredList>
            </div>
          </section>
        )}

        {/* Main Content */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Main Categories */}
              <div className="lg:col-span-3">
                {/* View Toggle */}
                <FadeIn>
                  <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center space-x-4">
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        All Categories
                      </h2>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {filteredCategories.length} categories available
                      </span>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => setViewMode('grid')}
                        className={`p-2 rounded-lg transition-colors duration-200 ${
                          viewMode === 'grid'
                            ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                            : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                        }`}
                        title="Grid View"
                      >
                        <Grid className="w-5 h-5" />
                      </button>
                      <button
                        type="button"
                        onClick={() => setViewMode('list')}
                        className={`p-2 rounded-lg transition-colors duration-200 ${
                          viewMode === 'list'
                            ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                            : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                        }`}
                        title="List View"
                      >
                        <List className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                </FadeIn>

                {/* Categories Grid/List */}
                <Suspense fallback={
                  <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 gap-6' : 'space-y-4'}>
                    {[...Array(6)].map((_, i) => (
                      <ArticleCardSkeleton key={i} />
                    ))}
                  </div>
                }>
                  <StaggeredList
                    delay={100}
                    className={
                      viewMode === 'grid'
                        ? 'grid grid-cols-1 md:grid-cols-2 gap-6'
                        : 'space-y-4'
                    }
                  >
                    {regularCategories.map((category) => (
                      <CategoryCard
                        key={category.id}
                        category={category}
                        variant={viewMode === 'list' ? 'compact' : 'default'}
                        locale="en"
                      />
                    ))}
                  </StaggeredList>
                </Suspense>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="space-y-8">
                  {/* Trending Categories */}
                  <FadeIn delay={600}>
                    <TrendingCategories
                      categories={trendingCategories}
                      locale="en"
                    />
                  </FadeIn>

                  {/* Quick Stats */}
                  <FadeIn delay={800}>
                    <div className="glass-card p-6">
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <BookOpen className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
                        Quick Stats
                      </h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Most Articles:</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">Civil Law</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Most Writers:</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">Civil Law</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Most Views:</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">Civil Law</span>
                        </div>
                      </div>
                    </div>
                  </FadeIn>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <BlogFooter />
    </div>
  );
}
