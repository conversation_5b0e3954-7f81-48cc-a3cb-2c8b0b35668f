import { BlogNavigation } from '@/components/blog-navigation'
import { CategoriesGrid } from '@/components/categories-grid'
import { BlogFooter } from '@/components/blog-footer'
import { getCategories } from '@/lib/blog-service'

export default async function CategoriesPage() {
  const categories = await getCategories()

  return (
    <div className="min-h-screen bg-gray-50">
      <BlogNavigation />
      <main className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              Legal Categories
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
              Explore articles organized by legal specializations and topics. Find content that matches your interests and academic focus.
            </p>
          </div>

          <CategoriesGrid categories={categories} />
        </div>
      </main>
      <BlogFooter />
    </div>
  )
}
