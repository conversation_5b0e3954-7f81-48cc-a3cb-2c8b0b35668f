import { notFound } from 'next/navigation'
import { BlogNavigation } from '@/components/blog-navigation'
import { WriterProfile } from '@/components/writer-profile'
import { BlogFooter } from '@/components/blog-footer'
import { getWriters, getWriterPosts } from '@/lib/blog-service'

interface WriterPageProps {
  params: {
    locale: string
    slug: string
  }
}

export default async function WriterPage({ params }: WriterPageProps) {
  const { slug } = await params
  
  // Get all writers and find the one matching the slug
  const writers = await getWriters()
  const writer = writers.find(w => 
    w.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '') === slug
  )

  if (!writer) {
    notFound()
  }

  const posts = await getWriterPosts(writer.id)

  return (
    <div className="min-h-screen bg-gray-50">
      <BlogNavigation />
      <main className="pt-16">
        <WriterProfile writer={writer} posts={posts} />
      </main>
      <BlogFooter />
    </div>
  )
}
