"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/writers/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/writers/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/[locale]/writers/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WritersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _components_blog_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/blog-navigation */ \"(app-pages-browser)/./src/components/blog-navigation.tsx\");\n/* harmony import */ var _components_blog_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/blog-footer */ \"(app-pages-browser)/./src/components/blog-footer.tsx\");\n/* harmony import */ var _components_ui_reading_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/reading-progress */ \"(app-pages-browser)/./src/components/ui/reading-progress.tsx\");\n/* harmony import */ var _components_ui_search__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/search */ \"(app-pages-browser)/./src/components/ui/search.tsx\");\n/* harmony import */ var _components_ui_writer_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/writer-card */ \"(app-pages-browser)/./src/components/ui/writer-card.tsx\");\n/* harmony import */ var _components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/page-transition */ \"(app-pages-browser)/./src/components/ui/page-transition.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,FileText,Grid,List,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Mock data for demonstration - replace with actual data fetching\nconst mockWriters = [\n    {\n        id: '1',\n        name: 'Sarah Johnson',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop',\n        bio: 'Passionate about constitutional law and digital rights. Currently researching the intersection of technology and privacy law for my law review note.',\n        university: 'Harvard Law School',\n        specialization: 'Constitutional Law & Technology',\n        location: 'Cambridge, MA',\n        articlesCount: 12,\n        totalViews: 15420,\n        joinedDate: '2023-09-15',\n        achievements: [\n            'Dean\\'s List',\n            'Law Review Editor',\n            'Moot Court Champion'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/sarahjohnson',\n            twitter: 'https://twitter.com/sarahjlaw'\n        },\n        featured: true,\n        // Career-focused additions\n        yearOfStudy: '3L (Third Year)',\n        gpa: '3.85',\n        lawReview: true,\n        mootCourt: true,\n        internships: [\n            'ACLU Summer Intern',\n            'DOJ Civil Rights Division'\n        ],\n        publications: [\n            'Harvard Law Review Note on Digital Privacy',\n            'Constitutional Law Quarterly Article'\n        ],\n        awards: [\n            'Dean\\'s List (6 semesters)',\n            'Constitutional Law Prize',\n            'Best Oralist - National Moot Court'\n        ],\n        barAdmissions: [\n            'Massachusetts (Expected 2024)'\n        ],\n        languages: [\n            'English (Native)',\n            'Spanish (Fluent)',\n            'French (Conversational)'\n        ],\n        interests: [\n            'Digital Rights',\n            'Privacy Law',\n            'First Amendment',\n            'Technology Policy'\n        ],\n        careerGoals: 'Seeking a federal clerkship followed by a position at a top-tier firm specializing in constitutional litigation and technology law.',\n        resumeUrl: '/resumes/sarah-johnson-resume.pdf',\n        portfolioUrl: '/portfolios/sarah-johnson'\n    },\n    {\n        id: '2',\n        name: 'Michael Chen',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop',\n        bio: 'International trade law enthusiast with a focus on Asia-Pacific economic relations and cross-border commerce regulations. Seeking to bridge legal practice with international business.',\n        university: 'Stanford Law School',\n        specialization: 'International Trade Law',\n        location: 'Palo Alto, CA',\n        articlesCount: 8,\n        totalViews: 12350,\n        joinedDate: '2023-08-20',\n        achievements: [\n            'International Law Society President',\n            'Trade Law Certificate'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/michaelchen'\n        },\n        featured: false,\n        yearOfStudy: '2L (Second Year)',\n        gpa: '3.72',\n        lawReview: false,\n        mootCourt: true,\n        internships: [\n            'WTO Legal Affairs Intern',\n            'Baker McKenzie Summer Associate'\n        ],\n        publications: [\n            'Stanford Journal of International Law Comment'\n        ],\n        awards: [\n            'International Law Society Outstanding Service',\n            'Asia-Pacific Law Student Award'\n        ],\n        barAdmissions: [\n            'California (Expected 2025)'\n        ],\n        languages: [\n            'English (Native)',\n            'Mandarin (Native)',\n            'Japanese (Intermediate)'\n        ],\n        interests: [\n            'International Trade',\n            'WTO Law',\n            'Cross-border M&A',\n            'Asia-Pacific Relations'\n        ],\n        careerGoals: 'Aiming for a position at an international law firm with strong Asia-Pacific practice, focusing on trade and investment law.',\n        resumeUrl: '/resumes/michael-chen-resume.pdf'\n    },\n    {\n        id: '3',\n        name: 'Emily Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=200&fit=crop',\n        bio: 'Criminal justice reform advocate working on policy research and community outreach programs for underserved populations. Committed to using law as a tool for social justice.',\n        university: 'Yale Law School',\n        specialization: 'Criminal Law & Policy',\n        location: 'New Haven, CT',\n        articlesCount: 15,\n        totalViews: 18750,\n        joinedDate: '2023-07-10',\n        achievements: [\n            'Public Interest Fellowship',\n            'Criminal Justice Clinic',\n            'Pro Bono Award'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/emilyrodriguez',\n            website: 'https://emilyrodriguez.law'\n        },\n        featured: true,\n        yearOfStudy: '3L (Third Year)',\n        gpa: '3.91',\n        lawReview: true,\n        mootCourt: false,\n        internships: [\n            'Public Defender\\'s Office',\n            'ACLU Criminal Law Reform Project',\n            'Innocence Project'\n        ],\n        publications: [\n            'Yale Law Journal Note on Sentencing Reform',\n            'Criminal Justice Policy Brief'\n        ],\n        awards: [\n            'Public Interest Fellowship',\n            'Outstanding Pro Bono Service (500+ hours)',\n            'Criminal Justice Reform Prize'\n        ],\n        barAdmissions: [\n            'Connecticut (Expected 2024)',\n            'New York (Expected 2024)'\n        ],\n        languages: [\n            'English (Native)',\n            'Spanish (Native)',\n            'Portuguese (Conversational)'\n        ],\n        interests: [\n            'Criminal Justice Reform',\n            'Sentencing Policy',\n            'Wrongful Convictions',\n            'Community Justice'\n        ],\n        careerGoals: 'Dedicated to public interest law, seeking a position with a public defender\\'s office or criminal justice reform organization.',\n        resumeUrl: '/resumes/emily-rodriguez-resume.pdf',\n        portfolioUrl: '/portfolios/emily-rodriguez'\n    },\n    {\n        id: '4',\n        name: 'David Kim',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop',\n        bio: 'Environmental law researcher focusing on climate change litigation and sustainable development policies.',\n        university: 'Columbia Law School',\n        specialization: 'Environmental Law',\n        location: 'New York, NY',\n        articlesCount: 10,\n        totalViews: 14200,\n        joinedDate: '2023-06-25',\n        achievements: [\n            'Environmental Law Review',\n            'Climate Justice Award'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/davidkim'\n        },\n        featured: false\n    },\n    {\n        id: '5',\n        name: 'Lisa Wang',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop',\n        bio: 'Corporate governance specialist with expertise in securities regulation and business ethics compliance.',\n        university: 'NYU Law School',\n        specialization: 'Corporate Law',\n        location: 'New York, NY',\n        articlesCount: 9,\n        totalViews: 11800,\n        joinedDate: '2023-05-15',\n        achievements: [\n            'Business Law Society VP',\n            'Securities Law Certificate'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/lisawang'\n        },\n        featured: false\n    },\n    {\n        id: '6',\n        name: 'Ahmed Hassan',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop',\n        bio: 'Human rights advocate specializing in international humanitarian law and refugee protection policies.',\n        university: 'Georgetown Law',\n        specialization: 'Human Rights Law',\n        location: 'Washington, DC',\n        articlesCount: 13,\n        totalViews: 16900,\n        joinedDate: '2023-04-30',\n        achievements: [\n            'Human Rights Fellowship',\n            'International Law Clinic',\n            'Refugee Advocacy Award'\n        ],\n        socialLinks: {\n            email: '<EMAIL>',\n            linkedin: 'https://linkedin.com/in/ahmedhassan',\n            twitter: 'https://twitter.com/ahmedlaw'\n        },\n        featured: true\n    }\n];\nconst stats = [\n    {\n        label: 'Law Students',\n        value: '150+',\n        icon: _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        label: 'Top Law Schools',\n        value: '50+',\n        icon: _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        label: 'Published Articles',\n        value: '500+',\n        icon: _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        label: 'Career Opportunities',\n        value: '1M+',\n        icon: _barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    }\n];\nfunction WritersPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_13__.useTranslations)();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [filteredWriters, setFilteredWriters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockWriters);\n    const handleSearch = (filters)=>{\n        console.log('Search filters:', filters);\n    // Implement filtering logic here\n    // For now, just log the filters\n    };\n    const featuredWriters = filteredWriters.filter((writer)=>writer.featured);\n    const regularWriters = filteredWriters.filter((writer)=>!writer.featured);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_reading_progress__WEBPACK_IMPORTED_MODULE_4__.ReadingProgress, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_navigation__WEBPACK_IMPORTED_MODULE_2__.BlogNavigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"gradient-text\",\n                                                    children: \"Future Legal Leaders\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto\",\n                                                children: \"Discover talented law students from top universities showcasing their expertise through published articles. Connect with future lawyers, explore their academic achievements, and view their professional portfolios.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 text-sm text-gray-500 dark:text-gray-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Resume-ready profiles • Portfolio showcases • Career networking\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    delay: 200,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12\",\n                                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-card p-6 text-center hover-lift\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        className: \"w-8 h-8 mx-auto mb-3 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white mb-1\",\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    delay: 400,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-2xl mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search__WEBPACK_IMPORTED_MODULE_5__.SearchComponent, {\n                                            onSearch: handleSearch,\n                                            placeholder: \"Search writers by name, university, or specialization...\",\n                                            showFilters: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    featuredWriters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50 dark:bg-gray-800/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                                                children: \"Rising Legal Stars\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-gray-600 dark:text-gray-400\",\n                                                children: \"Outstanding law students with exceptional academic records and professional achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 text-sm text-gray-500 dark:text-gray-400\",\n                                                children: \"These students have demonstrated excellence in academics, publications, and professional experience\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.StaggeredList, {\n                                    delay: 150,\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: featuredWriters.map((writer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_writer_card__WEBPACK_IMPORTED_MODULE_6__.WriterCard, {\n                                            writer: writer,\n                                            variant: \"detailed\",\n                                            locale: \"en\"\n                                        }, writer.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"All Writers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            filteredWriters.length,\n                                                            \" writers found\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setViewMode('grid'),\n                                                        className: \"p-2 rounded-lg transition-colors duration-200 \".concat(viewMode === 'grid' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'),\n                                                        title: \"Grid View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setViewMode('list'),\n                                                        className: \"p-2 rounded-lg transition-colors duration-200 \".concat(viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'),\n                                                        title: \"List View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_FileText_Grid_List_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-4',\n                                        children: [\n                                            ...Array(6)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.WriterCardSkeleton, {}, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, void 0))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_transition__WEBPACK_IMPORTED_MODULE_7__.StaggeredList, {\n                                        delay: 100,\n                                        className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-4',\n                                        children: regularWriters.map((writer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_writer_card__WEBPACK_IMPORTED_MODULE_6__.WriterCard, {\n                                                writer: writer,\n                                                variant: viewMode === 'list' ? 'compact' : 'default',\n                                                locale: \"en\"\n                                            }, writer.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_footer__WEBPACK_IMPORTED_MODULE_3__.BlogFooter, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\src\\\\app\\\\[locale]\\\\writers\\\\page.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(WritersPage, \"eW31QzDEIbyNN9UsN0Tr/aSg45c=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_13__.useTranslations\n    ];\n});\n_c = WritersPage;\nvar _c;\n$RefreshReg$(_c, \"WritersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vd3JpdGVycy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDQztBQUNrQjtBQUNSO0FBQ2E7QUFDVjtBQUNBO0FBQ2U7QUFDVjtBQUUwQjtBQUV4RixrRUFBa0U7QUFDbEUsTUFBTWtCLGNBQWM7SUFDbEI7UUFDRUMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsS0FBSztRQUNMQyxZQUFZO1FBQ1pDLGdCQUFnQjtRQUNoQkMsVUFBVTtRQUNWQyxlQUFlO1FBQ2ZDLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxjQUFjO1lBQUM7WUFBZ0I7WUFBcUI7U0FBc0I7UUFDMUVDLGFBQWE7WUFDWEMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLFNBQVM7UUFDWDtRQUNBQyxVQUFVO1FBQ1YsMkJBQTJCO1FBQzNCQyxhQUFhO1FBQ2JDLEtBQUs7UUFDTEMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLGFBQWE7WUFBQztZQUFzQjtTQUE0QjtRQUNoRUMsY0FBYztZQUFDO1lBQThDO1NBQXVDO1FBQ3BHQyxRQUFRO1lBQUM7WUFBOEI7WUFBNEI7U0FBcUM7UUFDeEdDLGVBQWU7WUFBQztTQUFnQztRQUNoREMsV0FBVztZQUFDO1lBQW9CO1lBQW9CO1NBQTBCO1FBQzlFQyxXQUFXO1lBQUM7WUFBa0I7WUFBZTtZQUFtQjtTQUFvQjtRQUNwRkMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGNBQWM7SUFDaEI7SUFDQTtRQUNFNUIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsS0FBSztRQUNMQyxZQUFZO1FBQ1pDLGdCQUFnQjtRQUNoQkMsVUFBVTtRQUNWQyxlQUFlO1FBQ2ZDLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxjQUFjO1lBQUM7WUFBdUM7U0FBd0I7UUFDOUVDLGFBQWE7WUFDWEMsT0FBTztZQUNQQyxVQUFVO1FBQ1o7UUFDQUUsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLEtBQUs7UUFDTEMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLGFBQWE7WUFBQztZQUE0QjtTQUFrQztRQUM1RUMsY0FBYztZQUFDO1NBQWdEO1FBQy9EQyxRQUFRO1lBQUM7WUFBaUQ7U0FBaUM7UUFDM0ZDLGVBQWU7WUFBQztTQUE2QjtRQUM3Q0MsV0FBVztZQUFDO1lBQW9CO1lBQXFCO1NBQTBCO1FBQy9FQyxXQUFXO1lBQUM7WUFBdUI7WUFBVztZQUFvQjtTQUF5QjtRQUMzRkMsYUFBYTtRQUNiQyxXQUFXO0lBQ2I7SUFDQTtRQUNFM0IsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsS0FBSztRQUNMQyxZQUFZO1FBQ1pDLGdCQUFnQjtRQUNoQkMsVUFBVTtRQUNWQyxlQUFlO1FBQ2ZDLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxjQUFjO1lBQUM7WUFBOEI7WUFBMkI7U0FBaUI7UUFDekZDLGFBQWE7WUFDWEMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZnQixTQUFTO1FBQ1g7UUFDQWQsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLEtBQUs7UUFDTEMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLGFBQWE7WUFBQztZQUE2QjtZQUFvQztTQUFvQjtRQUNuR0MsY0FBYztZQUFDO1lBQThDO1NBQWdDO1FBQzdGQyxRQUFRO1lBQUM7WUFBOEI7WUFBNkM7U0FBZ0M7UUFDcEhDLGVBQWU7WUFBQztZQUErQjtTQUEyQjtRQUMxRUMsV0FBVztZQUFDO1lBQW9CO1lBQW9CO1NBQThCO1FBQ2xGQyxXQUFXO1lBQUM7WUFBMkI7WUFBcUI7WUFBd0I7U0FBb0I7UUFDeEdDLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxjQUFjO0lBQ2hCO0lBQ0E7UUFDRTVCLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLEtBQUs7UUFDTEMsWUFBWTtRQUNaQyxnQkFBZ0I7UUFDaEJDLFVBQVU7UUFDVkMsZUFBZTtRQUNmQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsY0FBYztZQUFDO1lBQTRCO1NBQXdCO1FBQ25FQyxhQUFhO1lBQ1hDLE9BQU87WUFDUEMsVUFBVTtRQUNaO1FBQ0FFLFVBQVU7SUFDWjtJQUNBO1FBQ0VmLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLEtBQUs7UUFDTEMsWUFBWTtRQUNaQyxnQkFBZ0I7UUFDaEJDLFVBQVU7UUFDVkMsZUFBZTtRQUNmQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsY0FBYztZQUFDO1lBQTJCO1NBQTZCO1FBQ3ZFQyxhQUFhO1lBQ1hDLE9BQU87WUFDUEMsVUFBVTtRQUNaO1FBQ0FFLFVBQVU7SUFDWjtJQUNBO1FBQ0VmLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLEtBQUs7UUFDTEMsWUFBWTtRQUNaQyxnQkFBZ0I7UUFDaEJDLFVBQVU7UUFDVkMsZUFBZTtRQUNmQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsY0FBYztZQUFDO1lBQTJCO1lBQTRCO1NBQXlCO1FBQy9GQyxhQUFhO1lBQ1hDLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxTQUFTO1FBQ1g7UUFDQUMsVUFBVTtJQUNaO0NBQ0Q7QUFFRCxNQUFNZSxRQUFRO0lBQ1o7UUFBRUMsT0FBTztRQUFnQkMsT0FBTztRQUFRQyxNQUFNekMsOEhBQUtBO0lBQUM7SUFDcEQ7UUFBRXVDLE9BQU87UUFBbUJDLE9BQU87UUFBT0MsTUFBTXhDLCtIQUFLQTtJQUFDO0lBQ3REO1FBQUVzQyxPQUFPO1FBQXNCQyxPQUFPO1FBQVFDLE1BQU12QywrSEFBUUE7SUFBQztJQUM3RDtRQUFFcUMsT0FBTztRQUF3QkMsT0FBTztRQUFPQyxNQUFNdEMsK0hBQVVBO0lBQUM7Q0FDakU7QUFFYyxTQUFTdUM7O0lBQ3RCLE1BQU1DLElBQUlwRCwyREFBZUE7SUFDekIsTUFBTSxDQUFDcUQsVUFBVUMsWUFBWSxHQUFHdkQsK0NBQVFBLENBQWtCO0lBQzFELE1BQU0sQ0FBQ3dELGlCQUFpQkMsbUJBQW1CLEdBQUd6RCwrQ0FBUUEsQ0FBQ2lCO0lBRXZELE1BQU15QyxlQUFlLENBQUNDO1FBQ3BCQyxRQUFRQyxHQUFHLENBQUMsbUJBQW1CRjtJQUMvQixpQ0FBaUM7SUFDakMsZ0NBQWdDO0lBQ2xDO0lBRUEsTUFBTUcsa0JBQWtCTixnQkFBZ0JPLE1BQU0sQ0FBQ0MsQ0FBQUEsU0FBVUEsT0FBTy9CLFFBQVE7SUFDeEUsTUFBTWdDLGlCQUFpQlQsZ0JBQWdCTyxNQUFNLENBQUNDLENBQUFBLFNBQVUsQ0FBQ0EsT0FBTy9CLFFBQVE7SUFFeEUscUJBQ0UsOERBQUNpQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQy9ELDRFQUFlQTs7Ozs7MEJBQ2hCLDhEQUFDRix1RUFBY0E7Ozs7OzBCQUVmLDhEQUFDa0U7Z0JBQUtELFdBQVU7O2tDQUVkLDhEQUFDRTt3QkFBUUYsV0FBVTtrQ0FDakIsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzVELGtFQUFNQTs4Q0FDTCw0RUFBQzJEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0c7Z0RBQUdILFdBQVU7MERBQ1osNEVBQUNJO29EQUFLSixXQUFVOzhEQUFnQjs7Ozs7Ozs7Ozs7MERBRWxDLDhEQUFDSztnREFBRUwsV0FBVTswREFBd0U7Ozs7OzswREFJckYsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDSTtvREFBS0osV0FBVTs7c0VBQ2QsOERBQUNuRCwrSEFBUUE7NERBQUNtRCxXQUFVOzs7Ozs7c0VBQ3BCLDhEQUFDSTtzRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FPZCw4REFBQ2hFLGtFQUFNQTtvQ0FBQ2tFLE9BQU87OENBQ2IsNEVBQUNQO3dDQUFJQyxXQUFVO2tEQUNabkIsTUFBTTBCLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDaEIsOERBQUNWO2dEQUFnQkMsV0FBVTs7a0VBQ3pCLDhEQUFDUSxLQUFLeEIsSUFBSTt3REFBQ2dCLFdBQVU7Ozs7OztrRUFDckIsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUNaUSxLQUFLekIsS0FBSzs7Ozs7O2tFQUViLDhEQUFDZ0I7d0RBQUlDLFdBQVU7a0VBQ1pRLEtBQUsxQixLQUFLOzs7Ozs7OytDQU5MMkI7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FjaEIsOERBQUNyRSxrRUFBTUE7b0NBQUNrRSxPQUFPOzhDQUNiLDRFQUFDUDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQzlELGtFQUFlQTs0Q0FDZHdFLFVBQVVuQjs0Q0FDVm9CLGFBQVk7NENBQ1pDLGFBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFRdEJqQixnQkFBZ0JrQixNQUFNLEdBQUcsbUJBQ3hCLDhEQUFDWDt3QkFBUUYsV0FBVTtrQ0FDakIsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzVELGtFQUFNQTs4Q0FDTCw0RUFBQzJEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2M7Z0RBQUdkLFdBQVU7MERBQXdEOzs7Ozs7MERBR3RFLDhEQUFDSztnREFBRUwsV0FBVTswREFBMkM7Ozs7OzswREFHeEQsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUFnRDs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTW5FLDhEQUFDM0QseUVBQWFBO29DQUNaaUUsT0FBTztvQ0FDUE4sV0FBVTs4Q0FFVEwsZ0JBQWdCWSxHQUFHLENBQUMsQ0FBQ1YsdUJBQ3BCLDhEQUFDMUQsa0VBQVVBOzRDQUVUMEQsUUFBUUE7NENBQ1JrQixTQUFROzRDQUNSQyxRQUFPOzJDQUhGbkIsT0FBTzlDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FZMUIsOERBQUNtRDt3QkFBUUYsV0FBVTtrQ0FDakIsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQzVELGtFQUFNQTs4Q0FDTCw0RUFBQzJEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDYzt3REFBR2QsV0FBVTtrRUFBbUQ7Ozs7OztrRUFHakUsOERBQUNJO3dEQUFLSixXQUFVOzs0REFDYlgsZ0JBQWdCd0IsTUFBTTs0REFBQzs7Ozs7Ozs7Ozs7OzswREFJNUIsOERBQUNkO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2lCO3dEQUNDQyxNQUFLO3dEQUNMQyxTQUFTLElBQU0vQixZQUFZO3dEQUMzQlksV0FBVyxpREFJVixPQUhDYixhQUFhLFNBQ1Qsa0VBQ0E7d0RBRU5pQyxPQUFNO2tFQUVOLDRFQUFDekUsK0hBQUlBOzREQUFDcUQsV0FBVTs7Ozs7Ozs7Ozs7a0VBRWxCLDhEQUFDaUI7d0RBQ0NDLE1BQUs7d0RBQ0xDLFNBQVMsSUFBTS9CLFlBQVk7d0RBQzNCWSxXQUFXLGlEQUlWLE9BSENiLGFBQWEsU0FDVCxrRUFDQTt3REFFTmlDLE9BQU07a0VBRU4sNEVBQUN4RSwrSEFBSUE7NERBQUNvRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU94Qiw4REFBQ3BFLDJDQUFRQTtvQ0FBQ3lGLHdCQUNSLDhEQUFDdEI7d0NBQUlDLFdBQVdiLGFBQWEsU0FBUyx5REFBeUQ7a0RBQzVGOytDQUFJbUMsTUFBTTt5Q0FBRyxDQUFDZixHQUFHLENBQUMsQ0FBQ2dCLEdBQUdDLGtCQUNyQiw4REFBQ2xGLHVFQUFrQkEsTUFBTWtGOzs7Ozs7Ozs7OzhDQUk3Qiw0RUFBQ25GLHlFQUFhQTt3Q0FDWmlFLE9BQU87d0NBQ1BOLFdBQ0ViLGFBQWEsU0FDVCx5REFDQTtrREFHTFcsZUFBZVMsR0FBRyxDQUFDLENBQUNWLHVCQUNuQiw4REFBQzFELGtFQUFVQTtnREFFVDBELFFBQVFBO2dEQUNSa0IsU0FBUzVCLGFBQWEsU0FBUyxZQUFZO2dEQUMzQzZCLFFBQU87K0NBSEZuQixPQUFPOUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBWTVCLDhEQUFDZiwrREFBVUE7Ozs7Ozs7Ozs7O0FBR2pCO0dBdkx3QmlEOztRQUNabkQsdURBQWVBOzs7S0FESG1EIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEtyYWltYXRpY1xcRGVza3RvcFxcYWZlcnNhdGFcXGxhd2Jsb2dcXHNyY1xcYXBwXFxbbG9jYWxlXVxcd3JpdGVyc1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBTdXNwZW5zZSwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbnMgfSBmcm9tICduZXh0LWludGwnO1xuaW1wb3J0IHsgQmxvZ05hdmlnYXRpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvYmxvZy1uYXZpZ2F0aW9uJztcbmltcG9ydCB7IEJsb2dGb290ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvYmxvZy1mb290ZXInO1xuaW1wb3J0IHsgUmVhZGluZ1Byb2dyZXNzIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3JlYWRpbmctcHJvZ3Jlc3MnO1xuaW1wb3J0IHsgU2VhcmNoQ29tcG9uZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NlYXJjaCc7XG5pbXBvcnQgeyBXcml0ZXJDYXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3dyaXRlci1jYXJkJztcbmltcG9ydCB7IEZhZGVJbiwgU3RhZ2dlcmVkTGlzdCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9wYWdlLXRyYW5zaXRpb24nO1xuaW1wb3J0IHsgV3JpdGVyQ2FyZFNrZWxldG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NrZWxldG9uJztcbmltcG9ydCB7IENhcmVlckhpZ2hsaWdodHMgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZWVyLWhpZ2hsaWdodHMnO1xuaW1wb3J0IHsgVXNlcnMsIEF3YXJkLCBCb29rT3BlbiwgVHJlbmRpbmdVcCwgR3JpZCwgTGlzdCwgRmlsZVRleHQgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG4vLyBNb2NrIGRhdGEgZm9yIGRlbW9uc3RyYXRpb24gLSByZXBsYWNlIHdpdGggYWN0dWFsIGRhdGEgZmV0Y2hpbmdcbmNvbnN0IG1vY2tXcml0ZXJzID0gW1xuICB7XG4gICAgaWQ6ICcxJyxcbiAgICBuYW1lOiAnU2FyYWggSm9obnNvbicsXG4gICAgYXZhdGFyOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE0OTQ3OTAxMDg3NTUtMjYxNmI2MTJiNzg2P3c9MjAwJmg9MjAwJmZpdD1jcm9wJyxcbiAgICBiaW86ICdQYXNzaW9uYXRlIGFib3V0IGNvbnN0aXR1dGlvbmFsIGxhdyBhbmQgZGlnaXRhbCByaWdodHMuIEN1cnJlbnRseSByZXNlYXJjaGluZyB0aGUgaW50ZXJzZWN0aW9uIG9mIHRlY2hub2xvZ3kgYW5kIHByaXZhY3kgbGF3IGZvciBteSBsYXcgcmV2aWV3IG5vdGUuJyxcbiAgICB1bml2ZXJzaXR5OiAnSGFydmFyZCBMYXcgU2Nob29sJyxcbiAgICBzcGVjaWFsaXphdGlvbjogJ0NvbnN0aXR1dGlvbmFsIExhdyAmIFRlY2hub2xvZ3knLFxuICAgIGxvY2F0aW9uOiAnQ2FtYnJpZGdlLCBNQScsXG4gICAgYXJ0aWNsZXNDb3VudDogMTIsXG4gICAgdG90YWxWaWV3czogMTU0MjAsXG4gICAgam9pbmVkRGF0ZTogJzIwMjMtMDktMTUnLFxuICAgIGFjaGlldmVtZW50czogWydEZWFuXFwncyBMaXN0JywgJ0xhdyBSZXZpZXcgRWRpdG9yJywgJ01vb3QgQ291cnQgQ2hhbXBpb24nXSxcbiAgICBzb2NpYWxMaW5rczoge1xuICAgICAgZW1haWw6ICdzYXJhaC5qb2huc29uQGhhcnZhcmQuZWR1JyxcbiAgICAgIGxpbmtlZGluOiAnaHR0cHM6Ly9saW5rZWRpbi5jb20vaW4vc2FyYWhqb2huc29uJyxcbiAgICAgIHR3aXR0ZXI6ICdodHRwczovL3R3aXR0ZXIuY29tL3NhcmFoamxhdydcbiAgICB9LFxuICAgIGZlYXR1cmVkOiB0cnVlLFxuICAgIC8vIENhcmVlci1mb2N1c2VkIGFkZGl0aW9uc1xuICAgIHllYXJPZlN0dWR5OiAnM0wgKFRoaXJkIFllYXIpJyxcbiAgICBncGE6ICczLjg1JyxcbiAgICBsYXdSZXZpZXc6IHRydWUsXG4gICAgbW9vdENvdXJ0OiB0cnVlLFxuICAgIGludGVybnNoaXBzOiBbJ0FDTFUgU3VtbWVyIEludGVybicsICdET0ogQ2l2aWwgUmlnaHRzIERpdmlzaW9uJ10sXG4gICAgcHVibGljYXRpb25zOiBbJ0hhcnZhcmQgTGF3IFJldmlldyBOb3RlIG9uIERpZ2l0YWwgUHJpdmFjeScsICdDb25zdGl0dXRpb25hbCBMYXcgUXVhcnRlcmx5IEFydGljbGUnXSxcbiAgICBhd2FyZHM6IFsnRGVhblxcJ3MgTGlzdCAoNiBzZW1lc3RlcnMpJywgJ0NvbnN0aXR1dGlvbmFsIExhdyBQcml6ZScsICdCZXN0IE9yYWxpc3QgLSBOYXRpb25hbCBNb290IENvdXJ0J10sXG4gICAgYmFyQWRtaXNzaW9uczogWydNYXNzYWNodXNldHRzIChFeHBlY3RlZCAyMDI0KSddLFxuICAgIGxhbmd1YWdlczogWydFbmdsaXNoIChOYXRpdmUpJywgJ1NwYW5pc2ggKEZsdWVudCknLCAnRnJlbmNoIChDb252ZXJzYXRpb25hbCknXSxcbiAgICBpbnRlcmVzdHM6IFsnRGlnaXRhbCBSaWdodHMnLCAnUHJpdmFjeSBMYXcnLCAnRmlyc3QgQW1lbmRtZW50JywgJ1RlY2hub2xvZ3kgUG9saWN5J10sXG4gICAgY2FyZWVyR29hbHM6ICdTZWVraW5nIGEgZmVkZXJhbCBjbGVya3NoaXAgZm9sbG93ZWQgYnkgYSBwb3NpdGlvbiBhdCBhIHRvcC10aWVyIGZpcm0gc3BlY2lhbGl6aW5nIGluIGNvbnN0aXR1dGlvbmFsIGxpdGlnYXRpb24gYW5kIHRlY2hub2xvZ3kgbGF3LicsXG4gICAgcmVzdW1lVXJsOiAnL3Jlc3VtZXMvc2FyYWgtam9obnNvbi1yZXN1bWUucGRmJyxcbiAgICBwb3J0Zm9saW9Vcmw6ICcvcG9ydGZvbGlvcy9zYXJhaC1qb2huc29uJ1xuICB9LFxuICB7XG4gICAgaWQ6ICcyJyxcbiAgICBuYW1lOiAnTWljaGFlbCBDaGVuJyxcbiAgICBhdmF0YXI6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ3MjA5OTY0NTc4NS01NjU4YWJmNGZmNGU/dz0yMDAmaD0yMDAmZml0PWNyb3AnLFxuICAgIGJpbzogJ0ludGVybmF0aW9uYWwgdHJhZGUgbGF3IGVudGh1c2lhc3Qgd2l0aCBhIGZvY3VzIG9uIEFzaWEtUGFjaWZpYyBlY29ub21pYyByZWxhdGlvbnMgYW5kIGNyb3NzLWJvcmRlciBjb21tZXJjZSByZWd1bGF0aW9ucy4gU2Vla2luZyB0byBicmlkZ2UgbGVnYWwgcHJhY3RpY2Ugd2l0aCBpbnRlcm5hdGlvbmFsIGJ1c2luZXNzLicsXG4gICAgdW5pdmVyc2l0eTogJ1N0YW5mb3JkIExhdyBTY2hvb2wnLFxuICAgIHNwZWNpYWxpemF0aW9uOiAnSW50ZXJuYXRpb25hbCBUcmFkZSBMYXcnLFxuICAgIGxvY2F0aW9uOiAnUGFsbyBBbHRvLCBDQScsXG4gICAgYXJ0aWNsZXNDb3VudDogOCxcbiAgICB0b3RhbFZpZXdzOiAxMjM1MCxcbiAgICBqb2luZWREYXRlOiAnMjAyMy0wOC0yMCcsXG4gICAgYWNoaWV2ZW1lbnRzOiBbJ0ludGVybmF0aW9uYWwgTGF3IFNvY2lldHkgUHJlc2lkZW50JywgJ1RyYWRlIExhdyBDZXJ0aWZpY2F0ZSddLFxuICAgIHNvY2lhbExpbmtzOiB7XG4gICAgICBlbWFpbDogJ21pY2hhZWwuY2hlbkBzdGFuZm9yZC5lZHUnLFxuICAgICAgbGlua2VkaW46ICdodHRwczovL2xpbmtlZGluLmNvbS9pbi9taWNoYWVsY2hlbidcbiAgICB9LFxuICAgIGZlYXR1cmVkOiBmYWxzZSxcbiAgICB5ZWFyT2ZTdHVkeTogJzJMIChTZWNvbmQgWWVhciknLFxuICAgIGdwYTogJzMuNzInLFxuICAgIGxhd1JldmlldzogZmFsc2UsXG4gICAgbW9vdENvdXJ0OiB0cnVlLFxuICAgIGludGVybnNoaXBzOiBbJ1dUTyBMZWdhbCBBZmZhaXJzIEludGVybicsICdCYWtlciBNY0tlbnppZSBTdW1tZXIgQXNzb2NpYXRlJ10sXG4gICAgcHVibGljYXRpb25zOiBbJ1N0YW5mb3JkIEpvdXJuYWwgb2YgSW50ZXJuYXRpb25hbCBMYXcgQ29tbWVudCddLFxuICAgIGF3YXJkczogWydJbnRlcm5hdGlvbmFsIExhdyBTb2NpZXR5IE91dHN0YW5kaW5nIFNlcnZpY2UnLCAnQXNpYS1QYWNpZmljIExhdyBTdHVkZW50IEF3YXJkJ10sXG4gICAgYmFyQWRtaXNzaW9uczogWydDYWxpZm9ybmlhIChFeHBlY3RlZCAyMDI1KSddLFxuICAgIGxhbmd1YWdlczogWydFbmdsaXNoIChOYXRpdmUpJywgJ01hbmRhcmluIChOYXRpdmUpJywgJ0phcGFuZXNlIChJbnRlcm1lZGlhdGUpJ10sXG4gICAgaW50ZXJlc3RzOiBbJ0ludGVybmF0aW9uYWwgVHJhZGUnLCAnV1RPIExhdycsICdDcm9zcy1ib3JkZXIgTSZBJywgJ0FzaWEtUGFjaWZpYyBSZWxhdGlvbnMnXSxcbiAgICBjYXJlZXJHb2FsczogJ0FpbWluZyBmb3IgYSBwb3NpdGlvbiBhdCBhbiBpbnRlcm5hdGlvbmFsIGxhdyBmaXJtIHdpdGggc3Ryb25nIEFzaWEtUGFjaWZpYyBwcmFjdGljZSwgZm9jdXNpbmcgb24gdHJhZGUgYW5kIGludmVzdG1lbnQgbGF3LicsXG4gICAgcmVzdW1lVXJsOiAnL3Jlc3VtZXMvbWljaGFlbC1jaGVuLXJlc3VtZS5wZGYnXG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIG5hbWU6ICdFbWlseSBSb2RyaWd1ZXonLFxuICAgIGF2YXRhcjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDM4NzYxNjgxMDMzLTY0NjFmZmFkOGQ4MD93PTIwMCZoPTIwMCZmaXQ9Y3JvcCcsXG4gICAgYmlvOiAnQ3JpbWluYWwganVzdGljZSByZWZvcm0gYWR2b2NhdGUgd29ya2luZyBvbiBwb2xpY3kgcmVzZWFyY2ggYW5kIGNvbW11bml0eSBvdXRyZWFjaCBwcm9ncmFtcyBmb3IgdW5kZXJzZXJ2ZWQgcG9wdWxhdGlvbnMuIENvbW1pdHRlZCB0byB1c2luZyBsYXcgYXMgYSB0b29sIGZvciBzb2NpYWwganVzdGljZS4nLFxuICAgIHVuaXZlcnNpdHk6ICdZYWxlIExhdyBTY2hvb2wnLFxuICAgIHNwZWNpYWxpemF0aW9uOiAnQ3JpbWluYWwgTGF3ICYgUG9saWN5JyxcbiAgICBsb2NhdGlvbjogJ05ldyBIYXZlbiwgQ1QnLFxuICAgIGFydGljbGVzQ291bnQ6IDE1LFxuICAgIHRvdGFsVmlld3M6IDE4NzUwLFxuICAgIGpvaW5lZERhdGU6ICcyMDIzLTA3LTEwJyxcbiAgICBhY2hpZXZlbWVudHM6IFsnUHVibGljIEludGVyZXN0IEZlbGxvd3NoaXAnLCAnQ3JpbWluYWwgSnVzdGljZSBDbGluaWMnLCAnUHJvIEJvbm8gQXdhcmQnXSxcbiAgICBzb2NpYWxMaW5rczoge1xuICAgICAgZW1haWw6ICdlbWlseS5yb2RyaWd1ZXpAeWFsZS5lZHUnLFxuICAgICAgbGlua2VkaW46ICdodHRwczovL2xpbmtlZGluLmNvbS9pbi9lbWlseXJvZHJpZ3VleicsXG4gICAgICB3ZWJzaXRlOiAnaHR0cHM6Ly9lbWlseXJvZHJpZ3Vlei5sYXcnXG4gICAgfSxcbiAgICBmZWF0dXJlZDogdHJ1ZSxcbiAgICB5ZWFyT2ZTdHVkeTogJzNMIChUaGlyZCBZZWFyKScsXG4gICAgZ3BhOiAnMy45MScsXG4gICAgbGF3UmV2aWV3OiB0cnVlLFxuICAgIG1vb3RDb3VydDogZmFsc2UsXG4gICAgaW50ZXJuc2hpcHM6IFsnUHVibGljIERlZmVuZGVyXFwncyBPZmZpY2UnLCAnQUNMVSBDcmltaW5hbCBMYXcgUmVmb3JtIFByb2plY3QnLCAnSW5ub2NlbmNlIFByb2plY3QnXSxcbiAgICBwdWJsaWNhdGlvbnM6IFsnWWFsZSBMYXcgSm91cm5hbCBOb3RlIG9uIFNlbnRlbmNpbmcgUmVmb3JtJywgJ0NyaW1pbmFsIEp1c3RpY2UgUG9saWN5IEJyaWVmJ10sXG4gICAgYXdhcmRzOiBbJ1B1YmxpYyBJbnRlcmVzdCBGZWxsb3dzaGlwJywgJ091dHN0YW5kaW5nIFBybyBCb25vIFNlcnZpY2UgKDUwMCsgaG91cnMpJywgJ0NyaW1pbmFsIEp1c3RpY2UgUmVmb3JtIFByaXplJ10sXG4gICAgYmFyQWRtaXNzaW9uczogWydDb25uZWN0aWN1dCAoRXhwZWN0ZWQgMjAyNCknLCAnTmV3IFlvcmsgKEV4cGVjdGVkIDIwMjQpJ10sXG4gICAgbGFuZ3VhZ2VzOiBbJ0VuZ2xpc2ggKE5hdGl2ZSknLCAnU3BhbmlzaCAoTmF0aXZlKScsICdQb3J0dWd1ZXNlIChDb252ZXJzYXRpb25hbCknXSxcbiAgICBpbnRlcmVzdHM6IFsnQ3JpbWluYWwgSnVzdGljZSBSZWZvcm0nLCAnU2VudGVuY2luZyBQb2xpY3knLCAnV3JvbmdmdWwgQ29udmljdGlvbnMnLCAnQ29tbXVuaXR5IEp1c3RpY2UnXSxcbiAgICBjYXJlZXJHb2FsczogJ0RlZGljYXRlZCB0byBwdWJsaWMgaW50ZXJlc3QgbGF3LCBzZWVraW5nIGEgcG9zaXRpb24gd2l0aCBhIHB1YmxpYyBkZWZlbmRlclxcJ3Mgb2ZmaWNlIG9yIGNyaW1pbmFsIGp1c3RpY2UgcmVmb3JtIG9yZ2FuaXphdGlvbi4nLFxuICAgIHJlc3VtZVVybDogJy9yZXN1bWVzL2VtaWx5LXJvZHJpZ3Vlei1yZXN1bWUucGRmJyxcbiAgICBwb3J0Zm9saW9Vcmw6ICcvcG9ydGZvbGlvcy9lbWlseS1yb2RyaWd1ZXonXG4gIH0sXG4gIHtcbiAgICBpZDogJzQnLFxuICAgIG5hbWU6ICdEYXZpZCBLaW0nLFxuICAgIGF2YXRhcjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTA3MDAzMjExMTY5LTBhMWRkNzIyOGYyZD93PTIwMCZoPTIwMCZmaXQ9Y3JvcCcsXG4gICAgYmlvOiAnRW52aXJvbm1lbnRhbCBsYXcgcmVzZWFyY2hlciBmb2N1c2luZyBvbiBjbGltYXRlIGNoYW5nZSBsaXRpZ2F0aW9uIGFuZCBzdXN0YWluYWJsZSBkZXZlbG9wbWVudCBwb2xpY2llcy4nLFxuICAgIHVuaXZlcnNpdHk6ICdDb2x1bWJpYSBMYXcgU2Nob29sJyxcbiAgICBzcGVjaWFsaXphdGlvbjogJ0Vudmlyb25tZW50YWwgTGF3JyxcbiAgICBsb2NhdGlvbjogJ05ldyBZb3JrLCBOWScsXG4gICAgYXJ0aWNsZXNDb3VudDogMTAsXG4gICAgdG90YWxWaWV3czogMTQyMDAsXG4gICAgam9pbmVkRGF0ZTogJzIwMjMtMDYtMjUnLFxuICAgIGFjaGlldmVtZW50czogWydFbnZpcm9ubWVudGFsIExhdyBSZXZpZXcnLCAnQ2xpbWF0ZSBKdXN0aWNlIEF3YXJkJ10sXG4gICAgc29jaWFsTGlua3M6IHtcbiAgICAgIGVtYWlsOiAnZGF2aWQua2ltQGNvbHVtYmlhLmVkdScsXG4gICAgICBsaW5rZWRpbjogJ2h0dHBzOi8vbGlua2VkaW4uY29tL2luL2Rhdmlka2ltJ1xuICAgIH0sXG4gICAgZmVhdHVyZWQ6IGZhbHNlXG4gIH0sXG4gIHtcbiAgICBpZDogJzUnLFxuICAgIG5hbWU6ICdMaXNhIFdhbmcnLFxuICAgIGF2YXRhcjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDk0NzkwMTA4NzU1LTI2MTZiNjEyYjc4Nj93PTIwMCZoPTIwMCZmaXQ9Y3JvcCcsXG4gICAgYmlvOiAnQ29ycG9yYXRlIGdvdmVybmFuY2Ugc3BlY2lhbGlzdCB3aXRoIGV4cGVydGlzZSBpbiBzZWN1cml0aWVzIHJlZ3VsYXRpb24gYW5kIGJ1c2luZXNzIGV0aGljcyBjb21wbGlhbmNlLicsXG4gICAgdW5pdmVyc2l0eTogJ05ZVSBMYXcgU2Nob29sJyxcbiAgICBzcGVjaWFsaXphdGlvbjogJ0NvcnBvcmF0ZSBMYXcnLFxuICAgIGxvY2F0aW9uOiAnTmV3IFlvcmssIE5ZJyxcbiAgICBhcnRpY2xlc0NvdW50OiA5LFxuICAgIHRvdGFsVmlld3M6IDExODAwLFxuICAgIGpvaW5lZERhdGU6ICcyMDIzLTA1LTE1JyxcbiAgICBhY2hpZXZlbWVudHM6IFsnQnVzaW5lc3MgTGF3IFNvY2lldHkgVlAnLCAnU2VjdXJpdGllcyBMYXcgQ2VydGlmaWNhdGUnXSxcbiAgICBzb2NpYWxMaW5rczoge1xuICAgICAgZW1haWw6ICdsaXNhLndhbmdAbnl1LmVkdScsXG4gICAgICBsaW5rZWRpbjogJ2h0dHBzOi8vbGlua2VkaW4uY29tL2luL2xpc2F3YW5nJ1xuICAgIH0sXG4gICAgZmVhdHVyZWQ6IGZhbHNlXG4gIH0sXG4gIHtcbiAgICBpZDogJzYnLFxuICAgIG5hbWU6ICdBaG1lZCBIYXNzYW4nLFxuICAgIGF2YXRhcjogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDcyMDk5NjQ1Nzg1LTU2NThhYmY0ZmY0ZT93PTIwMCZoPTIwMCZmaXQ9Y3JvcCcsXG4gICAgYmlvOiAnSHVtYW4gcmlnaHRzIGFkdm9jYXRlIHNwZWNpYWxpemluZyBpbiBpbnRlcm5hdGlvbmFsIGh1bWFuaXRhcmlhbiBsYXcgYW5kIHJlZnVnZWUgcHJvdGVjdGlvbiBwb2xpY2llcy4nLFxuICAgIHVuaXZlcnNpdHk6ICdHZW9yZ2V0b3duIExhdycsXG4gICAgc3BlY2lhbGl6YXRpb246ICdIdW1hbiBSaWdodHMgTGF3JyxcbiAgICBsb2NhdGlvbjogJ1dhc2hpbmd0b24sIERDJyxcbiAgICBhcnRpY2xlc0NvdW50OiAxMyxcbiAgICB0b3RhbFZpZXdzOiAxNjkwMCxcbiAgICBqb2luZWREYXRlOiAnMjAyMy0wNC0zMCcsXG4gICAgYWNoaWV2ZW1lbnRzOiBbJ0h1bWFuIFJpZ2h0cyBGZWxsb3dzaGlwJywgJ0ludGVybmF0aW9uYWwgTGF3IENsaW5pYycsICdSZWZ1Z2VlIEFkdm9jYWN5IEF3YXJkJ10sXG4gICAgc29jaWFsTGlua3M6IHtcbiAgICAgIGVtYWlsOiAnYWhtZWQuaGFzc2FuQGdlb3JnZXRvd24uZWR1JyxcbiAgICAgIGxpbmtlZGluOiAnaHR0cHM6Ly9saW5rZWRpbi5jb20vaW4vYWhtZWRoYXNzYW4nLFxuICAgICAgdHdpdHRlcjogJ2h0dHBzOi8vdHdpdHRlci5jb20vYWhtZWRsYXcnXG4gICAgfSxcbiAgICBmZWF0dXJlZDogdHJ1ZVxuICB9XG5dO1xuXG5jb25zdCBzdGF0cyA9IFtcbiAgeyBsYWJlbDogJ0xhdyBTdHVkZW50cycsIHZhbHVlOiAnMTUwKycsIGljb246IFVzZXJzIH0sXG4gIHsgbGFiZWw6ICdUb3AgTGF3IFNjaG9vbHMnLCB2YWx1ZTogJzUwKycsIGljb246IEF3YXJkIH0sXG4gIHsgbGFiZWw6ICdQdWJsaXNoZWQgQXJ0aWNsZXMnLCB2YWx1ZTogJzUwMCsnLCBpY29uOiBCb29rT3BlbiB9LFxuICB7IGxhYmVsOiAnQ2FyZWVyIE9wcG9ydHVuaXRpZXMnLCB2YWx1ZTogJzFNKycsIGljb246IFRyZW5kaW5nVXAgfVxuXTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gV3JpdGVyc1BhZ2UoKSB7XG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoKTtcbiAgY29uc3QgW3ZpZXdNb2RlLCBzZXRWaWV3TW9kZV0gPSB1c2VTdGF0ZTwnZ3JpZCcgfCAnbGlzdCc+KCdncmlkJyk7XG4gIGNvbnN0IFtmaWx0ZXJlZFdyaXRlcnMsIHNldEZpbHRlcmVkV3JpdGVyc10gPSB1c2VTdGF0ZShtb2NrV3JpdGVycyk7XG5cbiAgY29uc3QgaGFuZGxlU2VhcmNoID0gKGZpbHRlcnM6IGFueSkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdTZWFyY2ggZmlsdGVyczonLCBmaWx0ZXJzKTtcbiAgICAvLyBJbXBsZW1lbnQgZmlsdGVyaW5nIGxvZ2ljIGhlcmVcbiAgICAvLyBGb3Igbm93LCBqdXN0IGxvZyB0aGUgZmlsdGVyc1xuICB9O1xuXG4gIGNvbnN0IGZlYXR1cmVkV3JpdGVycyA9IGZpbHRlcmVkV3JpdGVycy5maWx0ZXIod3JpdGVyID0+IHdyaXRlci5mZWF0dXJlZCk7XG4gIGNvbnN0IHJlZ3VsYXJXcml0ZXJzID0gZmlsdGVyZWRXcml0ZXJzLmZpbHRlcih3cml0ZXIgPT4gIXdyaXRlci5mZWF0dXJlZCk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy13aGl0ZSBkYXJrOmJnLWdyYXktOTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgPFJlYWRpbmdQcm9ncmVzcyAvPlxuICAgICAgPEJsb2dOYXZpZ2F0aW9uIC8+XG5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cInB0LTE2XCI+XG4gICAgICAgIHsvKiBIZWFkZXIgU2VjdGlvbiAqL31cbiAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHZpYS13aGl0ZSB0by1wdXJwbGUtNTAgZGFyazpmcm9tLWdyYXktOTAwIGRhcms6dmlhLWdyYXktOTAwIGRhcms6dG8tZ3JheS04MDAgcHktMjBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgICA8RmFkZUluPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIHNtOnRleHQtNHhsIGxnOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJncmFkaWVudC10ZXh0XCI+RnV0dXJlIExlZ2FsIExlYWRlcnM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHNtOnRleHQteGwgdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgIERpc2NvdmVyIHRhbGVudGVkIGxhdyBzdHVkZW50cyBmcm9tIHRvcCB1bml2ZXJzaXRpZXMgc2hvd2Nhc2luZyB0aGVpciBleHBlcnRpc2UgdGhyb3VnaCBwdWJsaXNoZWQgYXJ0aWNsZXMuXG4gICAgICAgICAgICAgICAgICBDb25uZWN0IHdpdGggZnV0dXJlIGxhd3llcnMsIGV4cGxvcmUgdGhlaXIgYWNhZGVtaWMgYWNoaWV2ZW1lbnRzLCBhbmQgdmlldyB0aGVpciBwcm9mZXNzaW9uYWwgcG9ydGZvbGlvcy5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IHRleHQtc20gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5SZXN1bWUtcmVhZHkgcHJvZmlsZXMg4oCiIFBvcnRmb2xpbyBzaG93Y2FzZXMg4oCiIENhcmVlciBuZXR3b3JraW5nPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvRmFkZUluPlxuXG4gICAgICAgICAgICB7LyogU3RhdHMgKi99XG4gICAgICAgICAgICA8RmFkZUluIGRlbGF5PXsyMDB9PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTYgbWF4LXctNHhsIG14LWF1dG8gbWItMTJcIj5cbiAgICAgICAgICAgICAgICB7c3RhdHMubWFwKChzdGF0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJnbGFzcy1jYXJkIHAtNiB0ZXh0LWNlbnRlciBob3Zlci1saWZ0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdGF0Lmljb24gY2xhc3NOYW1lPVwidy04IGgtOCBteC1hdXRvIG1iLTMgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3N0YXQudmFsdWV9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7c3RhdC5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0ZhZGVJbj5cblxuICAgICAgICAgICAgey8qIFNlYXJjaCAqL31cbiAgICAgICAgICAgIDxGYWRlSW4gZGVsYXk9ezQwMH0+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctMnhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICA8U2VhcmNoQ29tcG9uZW50XG4gICAgICAgICAgICAgICAgICBvblNlYXJjaD17aGFuZGxlU2VhcmNofVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggd3JpdGVycyBieSBuYW1lLCB1bml2ZXJzaXR5LCBvciBzcGVjaWFsaXphdGlvbi4uLlwiXG4gICAgICAgICAgICAgICAgICBzaG93RmlsdGVycz17dHJ1ZX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvRmFkZUluPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgey8qIEZlYXR1cmVkIFdyaXRlcnMgKi99XG4gICAgICAgIHtmZWF0dXJlZFdyaXRlcnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMTYgYmctZ3JheS01MCBkYXJrOmJnLWdyYXktODAwLzUwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgICAgIDxGYWRlSW4+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIFJpc2luZyBMZWdhbCBTdGFyc1xuICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgT3V0c3RhbmRpbmcgbGF3IHN0dWRlbnRzIHdpdGggZXhjZXB0aW9uYWwgYWNhZGVtaWMgcmVjb3JkcyBhbmQgcHJvZmVzc2lvbmFsIGFjaGlldmVtZW50c1xuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHRleHQtc20gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgVGhlc2Ugc3R1ZGVudHMgaGF2ZSBkZW1vbnN0cmF0ZWQgZXhjZWxsZW5jZSBpbiBhY2FkZW1pY3MsIHB1YmxpY2F0aW9ucywgYW5kIHByb2Zlc3Npb25hbCBleHBlcmllbmNlXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9GYWRlSW4+XG5cbiAgICAgICAgICAgICAgPFN0YWdnZXJlZExpc3RcbiAgICAgICAgICAgICAgICBkZWxheT17MTUwfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2ZlYXR1cmVkV3JpdGVycy5tYXAoKHdyaXRlcikgPT4gKFxuICAgICAgICAgICAgICAgICAgPFdyaXRlckNhcmRcbiAgICAgICAgICAgICAgICAgICAga2V5PXt3cml0ZXIuaWR9XG4gICAgICAgICAgICAgICAgICAgIHdyaXRlcj17d3JpdGVyfVxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZGV0YWlsZWRcIlxuICAgICAgICAgICAgICAgICAgICBsb2NhbGU9XCJlblwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L1N0YWdnZXJlZExpc3Q+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L3NlY3Rpb24+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEFsbCBXcml0ZXJzICovfVxuICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICAgIHsvKiBWaWV3IFRvZ2dsZSAqL31cbiAgICAgICAgICAgIDxGYWRlSW4+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLThcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICBBbGwgV3JpdGVyc1xuICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkV3JpdGVycy5sZW5ndGh9IHdyaXRlcnMgZm91bmRcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRWaWV3TW9kZSgnZ3JpZCcpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICB2aWV3TW9kZSA9PT0gJ2dyaWQnXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTEwMCBkYXJrOmJnLWJsdWUtOTAwIHRleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIGRhcms6aG92ZXI6dGV4dC1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiR3JpZCBWaWV3XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPEdyaWQgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFZpZXdNb2RlKCdsaXN0Jyl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMiByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgICAgICAgIHZpZXdNb2RlID09PSAnbGlzdCdcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtMTAwIGRhcms6YmctYmx1ZS05MDAgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgZGFyazpob3Zlcjp0ZXh0LWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJMaXN0IFZpZXdcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8TGlzdCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvRmFkZUluPlxuXG4gICAgICAgICAgICB7LyogV3JpdGVycyBHcmlkL0xpc3QgKi99XG4gICAgICAgICAgICA8U3VzcGVuc2UgZmFsbGJhY2s9e1xuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17dmlld01vZGUgPT09ICdncmlkJyA/ICdncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC04JyA6ICdzcGFjZS15LTQnfT5cbiAgICAgICAgICAgICAgICB7Wy4uLkFycmF5KDYpXS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgICAgICAgIDxXcml0ZXJDYXJkU2tlbGV0b24ga2V5PXtpfSAvPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIH0+XG4gICAgICAgICAgICAgIDxTdGFnZ2VyZWRMaXN0XG4gICAgICAgICAgICAgICAgZGVsYXk9ezEwMH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xuICAgICAgICAgICAgICAgICAgdmlld01vZGUgPT09ICdncmlkJ1xuICAgICAgICAgICAgICAgICAgICA/ICdncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC04J1xuICAgICAgICAgICAgICAgICAgICA6ICdzcGFjZS15LTQnXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3JlZ3VsYXJXcml0ZXJzLm1hcCgod3JpdGVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8V3JpdGVyQ2FyZFxuICAgICAgICAgICAgICAgICAgICBrZXk9e3dyaXRlci5pZH1cbiAgICAgICAgICAgICAgICAgICAgd3JpdGVyPXt3cml0ZXJ9XG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e3ZpZXdNb2RlID09PSAnbGlzdCcgPyAnY29tcGFjdCcgOiAnZGVmYXVsdCd9XG4gICAgICAgICAgICAgICAgICAgIGxvY2FsZT1cImVuXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvU3RhZ2dlcmVkTGlzdD5cbiAgICAgICAgICAgIDwvU3VzcGVuc2U+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvc2VjdGlvbj5cbiAgICAgIDwvbWFpbj5cblxuICAgICAgPEJsb2dGb290ZXIgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTdXNwZW5zZSIsInVzZVN0YXRlIiwidXNlVHJhbnNsYXRpb25zIiwiQmxvZ05hdmlnYXRpb24iLCJCbG9nRm9vdGVyIiwiUmVhZGluZ1Byb2dyZXNzIiwiU2VhcmNoQ29tcG9uZW50IiwiV3JpdGVyQ2FyZCIsIkZhZGVJbiIsIlN0YWdnZXJlZExpc3QiLCJXcml0ZXJDYXJkU2tlbGV0b24iLCJVc2VycyIsIkF3YXJkIiwiQm9va09wZW4iLCJUcmVuZGluZ1VwIiwiR3JpZCIsIkxpc3QiLCJGaWxlVGV4dCIsIm1vY2tXcml0ZXJzIiwiaWQiLCJuYW1lIiwiYXZhdGFyIiwiYmlvIiwidW5pdmVyc2l0eSIsInNwZWNpYWxpemF0aW9uIiwibG9jYXRpb24iLCJhcnRpY2xlc0NvdW50IiwidG90YWxWaWV3cyIsImpvaW5lZERhdGUiLCJhY2hpZXZlbWVudHMiLCJzb2NpYWxMaW5rcyIsImVtYWlsIiwibGlua2VkaW4iLCJ0d2l0dGVyIiwiZmVhdHVyZWQiLCJ5ZWFyT2ZTdHVkeSIsImdwYSIsImxhd1JldmlldyIsIm1vb3RDb3VydCIsImludGVybnNoaXBzIiwicHVibGljYXRpb25zIiwiYXdhcmRzIiwiYmFyQWRtaXNzaW9ucyIsImxhbmd1YWdlcyIsImludGVyZXN0cyIsImNhcmVlckdvYWxzIiwicmVzdW1lVXJsIiwicG9ydGZvbGlvVXJsIiwid2Vic2l0ZSIsInN0YXRzIiwibGFiZWwiLCJ2YWx1ZSIsImljb24iLCJXcml0ZXJzUGFnZSIsInQiLCJ2aWV3TW9kZSIsInNldFZpZXdNb2RlIiwiZmlsdGVyZWRXcml0ZXJzIiwic2V0RmlsdGVyZWRXcml0ZXJzIiwiaGFuZGxlU2VhcmNoIiwiZmlsdGVycyIsImNvbnNvbGUiLCJsb2ciLCJmZWF0dXJlZFdyaXRlcnMiLCJmaWx0ZXIiLCJ3cml0ZXIiLCJyZWd1bGFyV3JpdGVycyIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iLCJzZWN0aW9uIiwiaDEiLCJzcGFuIiwicCIsImRlbGF5IiwibWFwIiwic3RhdCIsImluZGV4Iiwib25TZWFyY2giLCJwbGFjZWhvbGRlciIsInNob3dGaWx0ZXJzIiwibGVuZ3RoIiwiaDIiLCJ2YXJpYW50IiwibG9jYWxlIiwiYnV0dG9uIiwidHlwZSIsIm9uQ2xpY2siLCJ0aXRsZSIsImZhbGxiYWNrIiwiQXJyYXkiLCJfIiwiaSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/writers/page.tsx\n"));

/***/ })

});